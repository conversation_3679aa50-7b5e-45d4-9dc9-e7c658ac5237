[project]
name = "igniteai-agent-sdk"
version = "0.1.1"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiofiles>=24.1.0",
    "aiohttp>=3.12.13",
    "docstring-parser>=0.16",
    "groq>=0.24.0",
    "hatchling>=1.27.0",
    "loguru>=0.7.3",
    "mcp[cli]>=1.6.0",
    "ollama>=0.4.8",
    "openai>=1.78.1",
    "pydantic>=2.10.6",
    "pydantic-settings>=2.8.1",
    "tiktoken>=0.9.0",
]

[project.scripts]
igniteai-agent-sdk = "igniteai_agent_sdk:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = ["pytest-asyncio>=0.26.0", "pytest>=8.3.5", "ruff>=0.11.6"]


[tool.ruff]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

line-length = 130
indent-width = 4
target-version = "py312"


[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "N",  # PEP8 naming convetions
    "D",  # pydocstyle
]

ignore = [
    "C901", # too complex
    "W191", # indentation contains tabs
    "D401", # imperative mood
    "D104",
    "D100",
    "D103",
    "D101",
    "D205",
    "D105",
    "D107",
    "D102",
    "N802",
]

fixable = ["ALL"]
unfixable = ["B"]
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = false
docstring-code-line-length = "dynamic"
