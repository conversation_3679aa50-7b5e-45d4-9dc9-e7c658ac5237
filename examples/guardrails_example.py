"""Example demonstrating the use of input and output guardrails with an agent."""

import asyncio
import re
from typing import Optional

from pydantic import BaseModel, Field

from igniteai_agent_sdk import Agent
from igniteai_agent_sdk.guardrails import GuardrailFunctionOutput, input_guardrail, output_guardrail


class ResponseContent(BaseModel):
    """Model for the structured output of the assistant."""

    answer: str = Field(..., description="The answer to the user's question")
    additional_info: Optional[str] = Field(None, description="Additional information if relevant")


async def provide_info(query: str) -> str:
    """A simple tool that provides information about a topic.

    Args:
        query: The topic to provide information about

    Returns:
        Information about the topic

    """
    info_database = {
        "python": "Python is a high-level, interpreted programming language known for its readability and versatility.",
        "javascript": "JavaScript is a scripting language that enables interactive web pages and is an essential part of web applications.",
        "guardrails": "Guardrails are safety mechanisms that help ensure AI systems operate within defined boundaries and constraints.",
        "ai": "Artificial Intelligence (AI) refers to systems or machines that mimic human intelligence to perform tasks and can iteratively improve based on information they collect.",
    }

    query_lower = query.lower()
    for key, value in info_database.items():
        if key in query_lower:
            return value

    return f"I don't have specific information about '{query}', but I can help with questions about Python, JavaScript, Guardrails, or AI."


@input_guardrail(name="PII_Detector", description="Detects and blocks personally identifiable information")
async def detect_pii(agent: Agent, input_message: str) -> GuardrailFunctionOutput:
    """Check if the input contains sensitive personal information."""
    # Credit card pattern (simplified for example purposes)
    credit_card_pattern = r"\b(?:\d{4}[- ]?){3}\d{4}\b"
    # Social security number pattern (simplified)
    ssn_pattern = r"\b\d{3}[-]?\d{2}[-]?\d{4}\b"

    contains_credit_card = bool(re.search(credit_card_pattern, input_message))
    contains_ssn = bool(re.search(ssn_pattern, input_message))

    if contains_credit_card or contains_ssn:
        return GuardrailFunctionOutput(
            tripwire_triggered=True,
            message="I cannot process messages containing sensitive information like credit card numbers or social security numbers.",
            output_info="PII detected",
        )

    return GuardrailFunctionOutput(tripwire_triggered=False)


@input_guardrail(name="Profanity_Filter", description="Blocks inputs containing profanity")
async def filter_profanity(agent: Agent, input_message: str) -> GuardrailFunctionOutput:
    """Check if the input contains profanity."""
    # Simple list of prohibited words (in a real system, you'd use a more comprehensive approach)
    prohibited_words = ["damn", "hell", "crap", "idiot"]

    lower_input = input_message.lower()
    for word in prohibited_words:
        if word in lower_input:
            return GuardrailFunctionOutput(
                tripwire_triggered=True,
                message="I cannot process messages containing inappropriate language.",
                output_info="Profanity detected",
            )

    return GuardrailFunctionOutput(tripwire_triggered=False)


@output_guardrail(name="Length_Limiter", description="Ensures responses don't exceed a certain length")
async def limit_response_length(agent: Agent, output: ResponseContent) -> GuardrailFunctionOutput:
    """Check if the output exceeds the maximum allowed length."""
    max_length = 500
    total_length = len(output.answer)
    if output.additional_info:
        total_length += len(output.additional_info)

    if total_length > max_length:
        # Apply simple length reduction instead of triggering guardrail error due to SDK bug
        shortened_answer = output.answer[: int(max_length / 2)]
        shortened_info = ""
        if output.additional_info:
            shortened_info = output.additional_info[: int(max_length / 2)]

        output.answer = shortened_answer + "... [truncated due to length]"
        output.additional_info = shortened_info

        return GuardrailFunctionOutput(tripwire_triggered=False, output_info=output)

    return GuardrailFunctionOutput(tripwire_triggered=False)


@output_guardrail(name="Content_Monitor", description="Ensures responses don't contain certain types of content")
async def monitor_content(agent: Agent, output: ResponseContent) -> GuardrailFunctionOutput:
    """Check if the output contains restricted content."""
    # Terms that should not appear in responses
    restricted_terms = ["confidential", "classified", "secret"]

    combined_output = output.answer
    if output.additional_info:
        combined_output += " " + output.additional_info

    lower_output = combined_output.lower()
    for term in restricted_terms:
        if term in lower_output:
            # Replace restricted content instead of triggering error due to SDK bug
            filtered_answer = output.answer.replace(term, "[REDACTED]")
            filtered_info = None
            if output.additional_info:
                filtered_info = output.additional_info.replace(term, "[REDACTED]")

            output.answer = filtered_answer
            output.additional_info = filtered_info

            return GuardrailFunctionOutput(tripwire_triggered=False, output_info=output)

    return GuardrailFunctionOutput(tripwire_triggered=False)


# Create the agent with guardrails
info_agent = Agent(
    name="Guardrails Demo Agent",
    role="Information Assistant",
    task="provide helpful and informative responses to user queries",
    instructions=(
        "You are an assistant that provides information on various topics. "
        "Use the provide_info tool to fetch information about topics. "
        "Always be helpful, concise, and informative. "
        "When you don't know something, acknowledge that and suggest what you can help with instead."
    ),
    tools=[provide_info],
    response_format=ResponseContent,
    input_guardrails=[detect_pii, filter_profanity],
    output_guardrails=[limit_response_length, monitor_content],
)


async def main():
    """Run the agent with various test messages to demonstrate guardrails."""
    print("=== Testing Normal Input ===")
    normal_message = "Can you tell me about Python programming?"
    result = await info_agent.run(normal_message)
    print(f"Input: {normal_message}")
    print(f"Output: {result.final_response}")
    print()

    try:
        print("=== Testing Input with PII (Credit Card) ===")
        pii_message = "My credit card number is 1234 5678 9012 3456. Can you store it for later?"
        result = await info_agent.run(pii_message)
        print(f"Input: {pii_message}")
        print(f"Output: {result.final_response}")
    except Exception as e:
        print(f"Input guardrail triggered: {str(e)}")
    print()

    try:
        print("=== Testing Input with Profanity ===")
        profanity_message = "This damn computer is driving me crazy. Can you help?"
        result = await info_agent.run(profanity_message)
        print(f"Input: {profanity_message}")
        print(f"Output: {result.final_response}")
    except Exception as e:
        print(f"Input guardrail triggered: {str(e)}")
    print()

    print("=== Testing Output Guardrail: Length Limiter ===")
    # This query should generate a longer response that will trigger the length limiter
    long_response_query = (
        "Can you tell me about AI and also about guardrails and also about Python and JavaScript all in one response?"
    )
    result = await info_agent.run(long_response_query)
    print(f"Input: {long_response_query}")
    print(f"Output: {result.final_response}")
    print()

    print("=== Testing Output Guardrail: Content Monitor ===")
    # To test the content monitor, we need to make the agent likely to use restricted terms
    restricted_content_query = "What kinds of information would be classified as secret?"
    result = await info_agent.run(restricted_content_query)
    print(f"Input: {restricted_content_query}")
    print(f"Output: {result.final_response}")
    print()


if __name__ == "__main__":
    asyncio.run(main())
