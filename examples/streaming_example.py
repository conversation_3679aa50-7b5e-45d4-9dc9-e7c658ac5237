import asyncio
import time

from igniteai_agent_sdk.agent import Agent
from igniteai_agent_sdk.tools import ToolKit


def calculate(expression: str) -> str:
    """Calculate mathematical expressions.

    Args:
        expression (str): The mathematical expression to evaluate.

    Returns:
        str: The result of the calculation or an error message.

    """
    try:
        result = eval(expression)
        return f"The result is: {result}"
    except Exception as e:
        return f"Error: {str(e)}"


def get_weather(city: str) -> str:
    """Get weather information for a city.

    Args:
        city (str): The name of the city.

    Returns:
        str: The weather information for the city.

    """
    # Simulate API call delay
    time.sleep(0.5)
    return f"Weather in {city}: Sunny, 72°F with light breeze"


async def streaming_with_tools_example():
    """Example of streaming with tool calls."""
    print("=== Streaming with Tools Example ===\n")

    agent = Agent(
        name="MathWeatherBot",
        introduction="I'm an assistant that can do math and get weather information",
        task="Help users with calculations and weather information",
        tools=ToolKit(tools=[calculate, get_weather]),
        verbose=True,
    )

    print("User: What's 25 * 17 + 100, and what's the weather like in New York?")
    print("Assistant: ", end="", flush=True)

    accumulated_content = ""

    async for chunk in agent.run_stream("What's 25 * 17 + 100, and what's the weather like in New York?"):
        if chunk.delta:
            print(chunk.delta, end="", flush=True)
            accumulated_content += chunk.delta

    print("\n" + "=" * 50 + "\n")


async def main():
    """Run all streaming examples."""
    print("IgniteAI Agent SDK - Streaming Examples\n")
    print("This demo showcases various streaming capabilities:\n")

    try:
        await streaming_with_tools_example()

        print("All streaming examples completed successfully!")

    except Exception as e:
        print(f"❌ Error running examples: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
