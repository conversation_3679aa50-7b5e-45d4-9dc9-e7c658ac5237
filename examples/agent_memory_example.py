import asyncio
from pathlib import Path

from igniteai_agent_sdk.agent import Agent


async def main():
    memory_dir = Path("./output/agent_memory")
    memory_dir.mkdir(parents=True, exist_ok=True)

    memory_agent = Agent(
        name="Memory Agent",
        introduction="I am an assistant that remembers our conversations across multiple runs.",
        memory_enabled=True,
        memory_type="file",
        memory_config={"directory": str(memory_dir), "filename": "agent_memory.json"},
        verbose=True,
    )

    user_input = ""
    while user_input.lower() != "exit":
        user_input = input("\nYou: ")
        if user_input.lower() == "exit":
            break

        print("\nAgent: ", end="", flush=True)
        response = await memory_agent.run(user_input, run_id="run_20250415153748_0086481d")
        print(response.final_response)

        print(f"(Run ID: {response.run_id})")


if __name__ == "__main__":
    asyncio.run(main())
