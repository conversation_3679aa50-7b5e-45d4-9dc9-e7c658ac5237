#!/usr/bin/env python3
"""Example demonstrating context sharing between multiple agents.

This example shows how multiple agents can:
1. Share a common context object
2. Update the context with their discoveries and findings
3. Access information added to context by other agents
4. Build upon each other's knowledge to solve tasks collaboratively
"""

import async<PERSON>
from typing import Any

from igniteai_agent_sdk._agent_context import Context
from igniteai_agent_sdk.agent import Agent
from igniteai_agent_sdk.tools.base_tool import wrap


@wrap
async def store_data(key: str, value: Any, context: Context) -> str:
    """Store data in the shared context.

    Args:
        key: The key under which to store the value
        value: The value to store
        context: The shared context object

    Returns:
        Confirmation message

    """
    await context.aupdate({key: value}, author="store_data_tool")
    return f"Successfully stored '{value}' under key '{key}' in the shared context"


@wrap
async def analyze_text(text: str, context: Context) -> str:
    """Analyze text and extract key information.

    Args:
        text: The text to analyze
        context: The shared context object

    Returns:
        Analysis results

    """
    # Simple word count analysis - in a real scenario, this could be more complex
    word_count = len(text.split())
    sentiment = "positive" if any(word in text.lower() for word in ["good", "great", "excellent"]) else "neutral"

    analysis = {
        "word_count": word_count,
        "sentiment": sentiment,
    }

    await context.aupdate({"text_analysis": analysis}, author="analyze_text_tool")
    return f"Text analyzed. Found {word_count} words with {sentiment} sentiment."


@wrap
async def retrieve_context(key: str, context: Context) -> str:
    """Retrieve data from the shared context.

    Args:
        key: specific key to retrieve
        context: The shared context object

    Returns:
        The requested context data as a string

    """
    current_context = await context.aget() if context else {}

    if key:
        if key in current_context:
            return f"Value for key '{key}': {current_context[key]}"
        else:
            return f"Key '{key}' not found in context"
    else:
        return f"Current context: {current_context}"


class AgentContextSharingExample:
    """Example class demonstrating context sharing between agents."""

    def __init__(self):
        # Create a shared context object
        self.shared_context = Context()

        # Research agent focuses on gathering information
        self.research_agent = Agent(
            name="ResearchAgent",
            introduction="You are a research assistant that gathers and organizes information.",
            role="Researcher",
            task="Research topics and store relevant information in the context for other agents to use.",
            tools=[store_data, analyze_text, retrieve_context],
            context=self.shared_context,  # Share the same context object
            verbose=False,
        )

        # Analyst agent focuses on analyzing information
        self.analyst_agent = Agent(
            name="AnalystAgent",
            introduction="You are an expert analyst who examines information and draws insights.",
            role="Analyst",
            task="Analyze data from the research agent and provide insights based on the findings.",
            tools=[store_data, analyze_text, retrieve_context],
            context=self.shared_context,  # Share the same context object
            verbose=False,
        )

        # Summary agent provides final summaries and recommendations
        self.summary_agent = Agent(
            name="SummaryAgent",
            introduction="You summarize findings and provide recommendations.",
            role="Advisor",
            task="Review all gathered information and analysis to provide a concise summary and recommendations.",
            tools=[store_data, analyze_text, retrieve_context],
            context=self.shared_context,  # Share the same context object
            verbose=False,
        )

    async def run(self):
        """Run the context sharing demonstration workflow."""
        print("\nSTEP 1: Research Agent gathering initial information...")
        research_response = await self.research_agent.run(
            user_msg="Research the impact of artificial intelligence on healthcare. Store your key findings in the shared context."
        )
        print(f"\nFinal Research: {research_response.final_response}")

        # Check context after research
        print("\nShared context after research:")
        context_data = await self.shared_context.aget()
        print(f"{context_data}\n")

        print("\nSTEP 2: Analyst Agent analyzing the research findings...")
        analyst_response = await self.analyst_agent.run(
            user_msg="Examine the research findings on AI in healthcare from the context. Analyze the implications and store your analysis."
        )
        print(f"\nFinal Analysis: {analyst_response.final_response}")

        # Check context after analysis
        print("\nShared context after analysis:")
        context_data = await self.shared_context.aget()
        print(f"{context_data}\n")

        print("\nSTEP 3: Summary Agent providing final recommendations...")
        summary_response = await self.summary_agent.run(
            user_msg="Provide a concise summary and recommendations based on all the research and analysis on AI in healthcare in the context."
        )
        print(f"\nFinal Summary: {summary_response.final_response}")

        # Final context state
        print("\nFinal shared context state:")
        final_context = await self.shared_context.aget()
        print(f"{final_context}")


async def main():
    example = AgentContextSharingExample()
    await example.run()


if __name__ == "__main__":
    asyncio.run(main())
