#!/usr/bin/env python
"""Agent Event Emitter Example.

This example demonstrates how to use the EventEmitter system to listen for
and handle various events emitted by agents during their execution.
"""

import asyncio
from datetime import datetime
from typing import Optional

from igniteai_agent_sdk.agent import Agent
from igniteai_agent_sdk.types import (
    AgentErrorEvent,
    AgentStartEvent,
    AgentStopEvent,
    Event,
    LLMResponseEvent,
    ToolCallEvent,
)


# Define a custom event for demonstration
class CustomAgentEvent(Event):
    """Custom event for agent-related notifications."""

    message: str
    timestamp: str = datetime.now().isoformat()
    agent_name: Optional[str] = None


def calculator(operation: str, a: float, b: float) -> str:
    """A simple calculator tool to demonstrate tool call events.

    Args:
        operation (str): The operation to perform (add, subtract, multiply, divide).
        a (float): The first operand.
        b (float): The second operand.

    Returns:
        str: The result of the operation.

    """
    if operation == "add":
        return f"{a} + {b} = {a + b}"
    elif operation == "subtract":
        return f"{a} - {b} = {a - b}"
    elif operation == "multiply":
        return f"{a} * {b} = {a * b}"
    elif operation == "divide":
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return f"{a} / {b} = {a / b}"
    else:
        return f"Unknown operation: {operation}. Supported operations are: add, subtract, multiply, divide."


async def handle_agent_start(event: AgentStartEvent) -> None:
    """Handler for agent start events."""
    print(f"\nAgent '{event.agent_name}' started with run ID: {event.run_id}")


async def handle_agent_stop(event: AgentStopEvent) -> None:
    """Handler for agent stop events."""
    print(f"\nAgent '{event.agent_name}' completed run: {event.run_id}")
    if event.response:
        print(f"Final response: {event.response.final_response[:100]}...")

        if event.response.usage:
            print("\nToken usage:")
            print(f"  - Prompt tokens: {event.response.usage.tokens.prompt_tokens}")
            print(f"  - Completion tokens: {event.response.usage.tokens.completion_tokens}")
            print(f"  - Total tokens: {event.response.usage.tokens.total_tokens}")


async def handle_agent_error(event: AgentErrorEvent) -> None:
    """Handler for agent error events."""
    print(f"\n❌ Agent '{event.agent_name}' encountered an error: {event.error}")


async def handle_llm_response(event: LLMResponseEvent) -> None:
    """Handler for LLM response events."""
    print(f"\nLLM ({event.model}) responded")
    if event.tool_calls:
        tool_count = len(event.tool_calls) if isinstance(event.tool_calls, list) else 1
        print(f"  - Response includes {tool_count} tool call(s)")


async def handle_tool_call(event: ToolCallEvent) -> None:
    """Handler for tool call events."""
    print(f"\nTool '{event.tool_name}' was called")
    print(f"  - Arguments: {event.arguments}")
    if event.duration_ms:
        print(f"  - Execution time: {event.duration_ms:.2f}ms")


async def handle_custom_event(event: CustomAgentEvent) -> None:
    """Handler for custom agent events."""
    print(f"\nCustom event from '{event.agent_name}': {event.message}")
    print(f"  - Timestamp: {event.timestamp}")


async def run_agent_with_events() -> None:
    """Run an agent with event handling."""
    # Create a standard agent with the calculator tool
    agent = Agent(
        name="EventDrivenAgent",
        tools=[calculator],
        verbose=True,
    )

    # Register event handlers
    agent.event_emitter.on(AgentStartEvent, handle_agent_start)
    agent.event_emitter.on(AgentStopEvent, handle_agent_stop)
    agent.event_emitter.on(AgentErrorEvent, handle_agent_error)
    agent.event_emitter.on(LLMResponseEvent, handle_llm_response)
    agent.event_emitter.on(ToolCallEvent, handle_tool_call)
    agent.event_emitter.on(CustomAgentEvent, handle_custom_event)

    # Emit a custom event
    await agent.event_emitter.emit(CustomAgentEvent(message="Agent initialized with calculator tool", agent_name=agent.name))

    # Run the agent with a query that will trigger tool use
    print("\nRunning agent with calculation prompt...")
    print(await agent.run(user_msg="Please calculate the following: 1. 24 * 7, 2. 144 / 12, 3. 99 - 76"))

    print("\nAgent execution complete!")


async def main() -> None:
    """Main entry point for the example."""
    print("=== Agent Event Emitter Example ===")
    print("This example demonstrates how the event emitter system works with agents.")

    # Run a standard agent with event handlers
    # await run_agent_with_events()

    print("\n" + "=" * 50 + "\n")

    # Run an agent with event handlers
    await run_agent_with_events()


if __name__ == "__main__":
    asyncio.run(main())
