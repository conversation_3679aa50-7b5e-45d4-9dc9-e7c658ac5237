import asyncio

from igniteai_agent_sdk.agent import Agent


def multiply(a: float, b: float) -> float:
    """Multiply two numbers together.

    Args:
        a: First number
        b: Second number
    Returns:
        Product of a and b

    """
    return a * b


def add(a: float, b: float) -> float:
    """Add two numbers together.

    Args:
        a: First number
        b: Second number
    Returns:
        Sum of a and b

    """
    return a + b


# Define more complex tools that will take longer to execute
async def slow_calculation(input_value: int) -> str:
    """Perform a slow calculation, simulating a complex operation.

    Args:
        input_value: The input value for the calculation
    Returns:
        Result of the calculation

    """
    await asyncio.sleep(1)  # Simulate a complex calculation
    result = 0
    for i in range(input_value):
        result += i
    return f"The sum of numbers from 0 to {input_value} is {result}"


async def main():
    # Create a Agent with some tools
    agent = Agent(
        name="Math Assistant",
        task="Help with math calculations.",
        tools=[multiply, add, slow_calculation],
        verbose=True,
    )

    # Run the agent with a user query
    print("\nRunning agent with a query that should require tool calls...")
    response = await agent.run(user_msg="What is 42 multiplied by 73? After that, calculate the sum of 10 and 10.")

    # Print the agent's response
    print(f"\nAgent Response: {response.final_response}")

    # Print usage information
    print(response.usage)


if __name__ == "__main__":
    asyncio.run(main())
