"""Example showing how to use LangChain tools with ignite-ai.

This example demonstrates:
1. Converting LangChain tools to ignite-ai tools
2. Using them with an Agent
3. Custom tool creation and combination with LangChain tools
"""

import asyncio

from langchain_community.tools import DuckDuckGoSearchRun, WikipediaQueryRun
from langchain_community.utilities import WikipediaAP<PERSON><PERSON>rapper

from igniteai_agent_sdk.agent.base_agent import Agent
from igniteai_agent_sdk.tools.base_tool import Too<PERSON>, wrap


@wrap
def calculate_sum(a: int, b: int) -> int:
    """Calculate the sum of two numbers.

    Args:
        a: First number
        b: Second number

    Returns:
        Sum of a and b

    """
    return a + b


@wrap
async def fetch_weather(location: str) -> str:
    """Fetch weather information for a location (demonstration only).

    Args:
        location: City name or location

    Returns:
        Weather information as text

    """
    # This is a mock implementation - in real usage, this would call a weather API
    await asyncio.sleep(1)  # Simulate API call
    return f"Weather for {location}: Sunny, 25°C"


class LangChainToolExample:
    def __init__(self):
        # Create some LangChain tools
        self.search_tool = DuckDuckGoSearchRun()
        self.wikipedia_tool = WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())  # type: ignore

    async def run(self):
        # Convert LangChain tools to ignite-ai tools
        search_ignite_tool = Tool.from_langchain_tool(self.search_tool)
        wikipedia_ignite_tool = Tool.from_langchain_tool(self.wikipedia_tool)

        # Create ignite-ai custom tools
        # calculator_tool = Tool.infer_from_callable(calculate_sum)
        # weather_tool = Tool.infer_from_callable(fetch_weather)

        # Print tool information
        print("Converted LangChain tools:")
        print(f"  - {search_ignite_tool.name}: {search_ignite_tool.description[:60]}...")
        print(f"  - {wikipedia_ignite_tool.name}: {wikipedia_ignite_tool.description[:60]}...")
        print("Custom ignite-ai tools:")
        print(f"  - {fetch_weather.name}: {fetch_weather.description}")
        print(f"  - {calculate_sum.name}: {calculate_sum.description}")

        # Create an agent with all tools
        agent = Agent(
            name="MultitoolAgent",
            tools=[search_ignite_tool, wikipedia_ignite_tool, calculate_sum, fetch_weather],
            instructions=(
                "You are a helpful assistant with access to various tools. Use the most appropriate tool for each query."
            ),
            verbose=True,
        )

        # Run the agent with a query that might use different tools
        print("\nRunning agent with tools...")
        query = "Calculate 245 + 389, and also tell me about the Golden Gate Bridge"
        print(f"Query: {query}\n")

        # Run the agent
        response = await agent.run(query)

        # Print final response
        print(f"\nFinal response: {response.final_response}")  # type: ignore

        # Print tool usage information
        print("\nTool usage information:")
        tool_calls = agent.usage_tracker.usage.tool_calls
        for tool_call in tool_calls:
            print(f"Tool: {tool_calls[tool_call].tool_name}")
            print(f"Duration: {tool_calls[tool_call].duration_ms:.2f}ms")
            print(f"Arguments: {tool_calls[tool_call].args}")
            print("---")


async def main():
    example = LangChainToolExample()
    await example.run()


if __name__ == "__main__":
    asyncio.run(main())
