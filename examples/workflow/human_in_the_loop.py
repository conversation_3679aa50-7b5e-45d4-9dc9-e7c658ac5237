import asyncio
import uuid
from dataclasses import dataclass
from typing import Any, Dict, Optional

from igniteai_agent_sdk.types import (
    Event,
    HumanApprovalRequestEvent,
    HumanApprovalResponseEvent,
    WorkflowStartEvent,
    WorkflowStopEvent,
)
from igniteai_agent_sdk.workflow.base import Workflow


# Custom workflow events
class DataValidationEvent(Event):
    """Event triggered when data needs validation."""

    data: Dict[str, Any]
    validation_context: str


class ValidationCompleteEvent(Event):
    """Event triggered when validation is complete."""

    validated_data: Dict[str, Any]
    approved: bool
    feedback: Optional[str] = None


class ProcessingCompleteEvent(Event):
    """Event triggered when processing is complete."""

    result: Any


@dataclass
class PendingApproval:
    """Stores information about pending approval requests."""

    request_id: str
    workflow_id: str
    task_id: str
    event: HumanApprovalRequestEvent
    workflow_instance: Optional["HumanInLoopWorkflow"] = None


class HumanInLoopWorkflow(Workflow):
    """A workflow that demonstrates pausing for user input and resuming execution.

    This workflow:
    1. Receives initial data
    2. Validates the data
    3. Pauses and requests human approval
    4. Waits for user input
    5. Resumes and processes the data based on approval
    6. Returns final result
    """

    def __init__(self, name: str = "HumanInLoopWorkflow"):
        super().__init__(name=name)
        self.pending_approvals: Dict[str, PendingApproval] = {}
        self.approval_responses: Dict[str, HumanApprovalResponseEvent] = {}

        # Set up event handlers
        self._setup_handlers()

    def _setup_handlers(self):
        """Set up all workflow event handlers."""

        @self.task(WorkflowStartEvent, metadata={"description": "Initialize the workflow"})
        async def start_workflow(event: WorkflowStartEvent) -> DataValidationEvent:
            print(f"Starting workflow {self.workflow_id}")
            print(f"Received initial data: {event.model_dump()}")

            # Extract data from the start event
            data = {k: v for k, v in event.model_dump().items() if k != "workflow_id"}

            return DataValidationEvent(data=data, validation_context="Initial data processing workflow")

        @self.task(DataValidationEvent, metadata={"description": "Validate data and request approval"})
        async def validate_data(event: DataValidationEvent) -> HumanApprovalRequestEvent:
            print(f"Validating data: {event.data}")

            # Simulate data validation logic
            validation_summary = self._create_validation_summary(event.data)

            # Create approval request
            request_id = str(uuid.uuid4())
            approval_request = HumanApprovalRequestEvent(
                workflow_id=self.workflow_id,
                task_id="data_validation",
                request_id=request_id,
                prompt=f"Please review and approve the following data:\n\n{validation_summary}\n\nApprove?",
                data=event.data,
                options=["approve", "reject", "modify"],
            )

            # Store pending approval
            pending = PendingApproval(
                request_id=request_id,
                workflow_id=self.workflow_id,
                task_id="data_validation",
                event=approval_request,
                workflow_instance=self,
            )
            self.pending_approvals[request_id] = pending

            # Pause the workflow
            await self.pause()

            return approval_request

        @self.task(HumanApprovalResponseEvent, metadata={"description": "Process human approval response"})
        async def process_approval_response(event: HumanApprovalResponseEvent) -> ValidationCompleteEvent:
            print(f"Received human response: {event.model_dump()}")

            # Store the response
            self.approval_responses[event.request_id] = event

            # Remove from pending approvals
            if event.request_id in self.pending_approvals:
                pending = self.pending_approvals.pop(event.request_id)
                approved_data = pending.event.data or {}

                # If user provided modified data, use it
                if event.data:
                    approved_data.update(event.data)

                print(f"Resuming workflow with approval: {event.approved}")

                # Resume the workflow
                await self.resume()

                return ValidationCompleteEvent(validated_data=approved_data, approved=event.approved, feedback=event.feedback)
            else:
                raise ValueError(f"No pending approval found for request_id: {event.request_id}")

        @self.task(ValidationCompleteEvent, metadata={"description": "Process validated data"})
        async def process_validated_data(event: ValidationCompleteEvent) -> ProcessingCompleteEvent:
            if event.approved:
                print(f"Processing approved data: {event.validated_data}")

                # Simulate data processing
                await asyncio.sleep(1)  # Simulate processing time
                result = self._process_data(event.validated_data)

                print(f"Processing complete: {result}")

                return ProcessingCompleteEvent(result=result)
            else:
                print(f"❌ Data rejected. Feedback: {event.feedback}")
                return ProcessingCompleteEvent(result={"status": "rejected", "reason": event.feedback})

        @self.task(ProcessingCompleteEvent, metadata={"description": "Complete the workflow"})
        async def complete_workflow(event: ProcessingCompleteEvent) -> WorkflowStopEvent:
            print(f"Workflow completed with result: {event.result}")

            return WorkflowStopEvent(result=event.result)

    def _create_validation_summary(self, data: Dict[str, Any]) -> str:
        """Create a human-readable validation summary."""
        summary_lines = ["Data Summary:"]
        for key, value in data.items():
            summary_lines.append(f"  • {key}: {value}")
        return "\n".join(summary_lines)

    def _process_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate data processing logic."""
        processed = {}
        for key, value in data.items():
            if isinstance(value, str):
                processed[f"processed_{key}"] = value.upper()
            elif isinstance(value, (int, float)):
                processed[f"processed_{key}"] = value * 2
            else:
                processed[f"processed_{key}"] = str(value)

        processed["processing_timestamp"] = asyncio.get_event_loop().time()
        processed["status"] = "completed"
        return processed

    async def submit_approval_response(
        self, request_id: str, approved: bool, feedback: Optional[str] = None, modified_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Submit an approval response for a pending request.

        Args:
            request_id: The ID of the approval request
            approved: Whether the request is approved
            feedback: Optional feedback from the user
            modified_data: Optional modified data if user wants to change something

        Returns:
            True if the response was successfully submitted, False otherwise

        """
        if request_id not in self.pending_approvals:
            print(f"❌ No pending approval found for request_id: {request_id}")
            return False

        pending = self.pending_approvals[request_id]

        response_event = HumanApprovalResponseEvent(
            workflow_id=pending.workflow_id,
            task_id=pending.task_id,
            request_id=request_id,
            approved=approved,
            feedback=feedback,
            data=modified_data,
        )

        # Emit the response event to continue the workflow
        await self.event_emitter.emit(response_event)

        print(f"Submitted approval response for request {request_id}")
        return True

    def get_pending_approvals(self) -> Dict[str, Dict[str, Any]]:
        """Get all pending approval requests."""
        return {
            request_id: {
                "request_id": pending.request_id,
                "workflow_id": pending.workflow_id,
                "task_id": pending.task_id,
                "prompt": pending.event.prompt,
                "data": pending.event.data,
                "options": pending.event.options,
                "status": pending.event.status,
            }
            for request_id, pending in self.pending_approvals.items()
        }


# Example usage and demonstration
async def main():
    """Demonstrate the human-in-the-loop workflow."""
    print("=" * 80)
    print("Human-in-the-Loop Workflow Demonstration")
    print("=" * 80)

    # Create workflow instance
    workflow = HumanInLoopWorkflow()

    # Initial data for the workflow
    initial_data = {
        "user_id": "12345",
        "order_amount": 1500.00,
        "product_category": "electronics",
        "special_instructions": "Handle with care - fragile items",
    }

    print(f"Starting workflow with data: {initial_data}")
    print()

    # Start the workflow in a separate task so we can handle approvals
    workflow_task = asyncio.create_task(workflow.start(initial_data))

    # Wait a bit for the workflow to reach the approval point
    await asyncio.sleep(2)

    print("⏳ Waiting for workflow to reach approval point...")

    # Check for pending approvals
    pending = workflow.get_pending_approvals()

    if pending:
        print("Pending approvals found:")
        for request_id, approval_info in pending.items():
            print(f"  Request ID: {request_id}")
            print(f"  Prompt: {approval_info['prompt']}")
            print(f"  Options: {approval_info['options']}")
            print()

            # Simulate user decision (in real implementation, this would come from API/UI)
            print("Simulating user approval...")
            print("Options: approve, reject, modify")

            # For demonstration, we'll auto-approve after a short delay
            await asyncio.sleep(1)

            # Simulate user choice - you can change this to test different scenarios
            user_choice = input("Enter your choice: ")  # Change to "reject" or "modify" to test other flows

            print(f"User selected: {user_choice}")

            if user_choice == "approve":
                await workflow.submit_approval_response(
                    request_id=request_id,
                    approved=True,
                    feedback="Looks good, approved for processing",
                    modified_data={"priority": "high"},  # User adds priority
                )
            elif user_choice == "reject":
                await workflow.submit_approval_response(
                    request_id=request_id,
                    approved=False,
                    feedback="Data validation failed - rejected by user",
                )
            elif user_choice == "modify":
                await workflow.submit_approval_response(
                    request_id=request_id,
                    approved=True,
                    feedback="Approved with modifications",
                    modified_data={
                        "priority": "high",
                        "order_amount": 1200.00,  # Modified amount
                        "additional_notes": "Modified by user review",
                    },
                )
            else:
                print(f"❌ Invalid choice: {user_choice}")
                await workflow.submit_approval_response(
                    request_id=request_id,
                    approved=False,
                    feedback="Invalid user input",
                )
            break

    # Wait for workflow completion
    try:
        result = await asyncio.wait_for(workflow_task, timeout=30.0)
        print()
        print("Final Workflow Result:")
        if result is not None:
            print(f"   {result}")
        else:
            print("   None (This indicates an issue with result capture)")
    except asyncio.TimeoutError:
        print("Workflow timed out")
        workflow_task.cancel()

    # Cleanup
    await workflow.cleanup()
    print()
    print("=" * 80)
    print("Demonstration complete")
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())
