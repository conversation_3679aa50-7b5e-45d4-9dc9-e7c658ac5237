import asyncio
from typing import List

from igniteai_agent_sdk.types import Event, WorkflowStartEvent, WorkflowStopEvent
from igniteai_agent_sdk.workflow.base import Workflow


class ProcessDataEvent(Event):
    data: List[int]


class DataProcessedEvent(Event):
    result: int


class FinalResultEvent(Event):
    value: int


async def main():
    workflow = Workflow(name="Data Processing Example")

    @workflow.task(WorkflowStartEvent, metadata={"description": "Starts the workflow"})
    def start_workflow(event: WorkflowStartEvent) -> ProcessDataEvent:
        print("Starting workflow with decorator...")

        data = event.data if hasattr(event, "data") else []
        return ProcessDataEvent(data=data)

    @workflow.task(ProcessDataEvent, metadata={"description": "Processes raw data"})
    def process_data(event: ProcessDataEvent) -> DataProcessedEvent:
        print(f"Processing data: {event.data}")
        result = sum(event.data)

        return DataProcessedEvent(result=result)

    @workflow.task(DataProcessedEvent, metadata={"description": "Calculates the final result"})
    async def calculate_final_result(event: DataProcessedEvent) -> WorkflowStopEvent:
        print(f"Calculating final result from: {event.result}")
        value = event.result * 2
        return WorkflowStopEvent(result=value)

    initial_data = {"data": [1, 2, 3, 4, 5]}

    print("\nStarting workflow...")
    print(await workflow.start(initial_data))

    await workflow.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
