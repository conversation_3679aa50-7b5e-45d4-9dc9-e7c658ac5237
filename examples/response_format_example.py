import asyncio

from pydantic import BaseModel, Field

from igniteai_agent_sdk.agent import Agent


class Answer(BaseModel):
    explanation: str = Field(..., description="A detailed explanation of how you solved the problem.")
    final_answer: int = Field(..., description="Final answer to the problem.")


class Answers(BaseModel):
    answers: list[Answer] = Field(..., description="List of answers to the problem.")


def add(num1: int, num2: int) -> int:
    """Adds two numbers together.

    Args:
        num1 (int): The first number to add.
        num2 (int): The second number to add.

    Returns:
        int: The sum of the two numbers.

    """
    return num1 + num2


def multiply(num1: int, num2: int) -> int:
    """Multiply two numbers together.

    Args:
        num1 (int): The first number to add.
        num2 (int): The second number to add.

    Returns:
        int: The multiplication of the two numbers.

    """
    return num1 * num2


agent = Agent(
    name="Math agent",
    introduction=("You are a helpful assistant who is an expert in Mathematics."),
    role="Mathematician",
    task="Given a user mathematical problem, solve it and provide a detailed explanation.",
    tools=[add, multiply],
    response_format=Answers,
)


async def main():
    async for chunk in agent.run_stream(user_msg="what is 2+5 and then tell me what is 5*4"):
        if chunk.is_final:
            print(chunk)


print(asyncio.run(main()))
