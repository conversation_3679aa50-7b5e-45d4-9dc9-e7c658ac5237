import asyncio

from igniteai_agent_sdk.agent import Agent


async def simple_streaming_demo():
    """Simple demo of streaming agent responses."""
    print("🤖 Simple Streaming Demo\n")

    # Create a basic agent
    agent = Agent(
        name="StreamBot", introduction="You are a helpful AI assistant", task="Provide streaming responses to user questions"
    )

    # Ask a question and stream the response
    question = "Tell me an interesting fact about space"
    print(f"User: {question}")
    print("Assistant: ", end="", flush=True)

    # Stream the response chunk by chunk
    async for chunk in agent.run_stream(question):
        if chunk.delta:
            # Print each piece of text as it arrives
            print(chunk.delta, end="", flush=True)

        if chunk.is_final:
            print(f"\n\n✅ Streaming complete! ({chunk.chunk_index + 1} chunks total)")
            break


async def main():
    try:
        await simple_streaming_demo()
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
