import asyncio
from pathlib import Path

from igniteai_agent_sdk import Agent
from igniteai_agent_sdk.tools.mcp import MCPServer

agent = Agent(
    name="Agent",
    introduction="You are a helpful assistant. Help users with their queries using the tools available to you.",
    mcp_servers=[
        MCPServer(
            name="filesystem server",
            command="npx",
            args=["-y", "@modelcontextprotocol/server-filesystem", str(Path(__file__).parent.parent.parent)],
        ),
        MCPServer(name="weather server", sse_url="http://localhost:8080/sse"),
    ],
)


async def main():
    # print(await agent.run(user_msg="can you give me a detailed summary of the license of this project?"))
    print(await agent.run(user_msg="what is the weather like in New York today?"))


asyncio.run(main())
