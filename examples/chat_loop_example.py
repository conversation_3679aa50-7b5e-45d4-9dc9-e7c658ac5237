import asyncio

from igniteai_agent_sdk.agent import Agent
from igniteai_agent_sdk.tools.base_tool import Tool
from igniteai_agent_sdk.tools.toolkit import ToolKit
from igniteai_agent_sdk.utils import interactive_chat_loop


async def run_simple_chat():
    print("\n--- Example 1: Simple Agent Chat ---\n")
    agent = Agent(
        name="Simple<PERSON><PERSON>",
        introduction="You are a friendly and helpful assistant.",
        verbose=True,
    )

    await interactive_chat_loop(
        agent,
        welcome_message="Hello! I'm a simple chat assistant. Ask me anything!",
        max_turns=3,
    )


async def run_react_chat():
    print("\n--- Example 2: ReAct Agent with Tools ---\n")

    async def calculate(expression: str) -> str:
        """Calculate the result of a mathematical expression.

        Args:
            expression: A mathematical expression as a string (e.g. "2+2*3")

        Returns:
            The result as a string

        """
        try:
            result = eval(expression, {"__builtins__": {}})
            return f"The result of {expression} is {result}"
        except Exception as e:
            return f"Error calculating {expression}: {str(e)}"

    async def get_date() -> str:
        """Get the current date and time.

        Returns:
            The current date and time

        """
        from datetime import datetime

        now = datetime.now()
        return f"The current date and time is {now.strftime('%Y-%m-%d %H:%M:%S')}"

    toolkit = ToolKit([Tool.infer_from_callable(calculate), Tool.infer_from_callable(get_date)])

    agent = Agent(
        name="ToolAssistant",
        role="helpful assistant",
        task="Help users with calculations and provide date information",
        instructions="You are a helpful assistant that can perform calculations and provide the current date and time.",
        tools=toolkit,
        verbose=True,
    )

    await interactive_chat_loop(
        agent,
        welcome_message="Hello! I'm an assistant that can help with calculations and tell you the date.\nTry asking me to calculate something or get the current date.\nType 'exit' to end the chat.",
        max_turns=5,
    )


async def main():
    """Run the chat loop examples one after another."""
    await run_simple_chat()
    # await run_react_chat()


if __name__ == "__main__":
    asyncio.run(main())
