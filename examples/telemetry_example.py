import asyncio
from pathlib import Path

from igniteai_agent_sdk.agent import Agent
from igniteai_agent_sdk.tools.base_tool import Tool


async def calculate(expression: str) -> str:
    """Calculate the result of a mathematical expression.

    Args:
        expression (str): The mathematical expression to evaluate.

    Returns:
        str: The result of the calculation or an error message.

    """
    try:
        result = eval(expression, {"__builtins__": {}}, {"abs": abs, "max": max, "min": min})
        return f"Result: {result}"
    except Exception as e:
        return f"Error calculating: {str(e)}"


async def fetch_weather(location: str) -> str:
    """Simulate fetching weather data for a location.

    Args:
        location (str): The location to fetch weather for.

    Returns:
        str: A string describing the weather in the location.

    """
    weather_data = {
        "new york": {"condition": "sunny", "temperature": 72},
        "london": {"condition": "rainy", "temperature": 62},
        "tokyo": {"condition": "cloudy", "temperature": 68},
        "sydney": {"condition": "clear", "temperature": 80},
    }

    location = location.lower()
    if location in weather_data:
        data = weather_data[location]
        return f"Weather in {location.title()}: {data['condition'].title()}, {data['temperature']}°F"
    else:
        return f"Weather data for {location} not available."


async def main():
    agent = Agent(
        name="TraceAgent",
        task="Help users with calculations and weather information.",
        tools=[
            Tool.infer_from_callable(calculate),
            Tool.infer_from_callable(fetch_weather),
        ],
        verbose=True,
    )
    print("Running agent with tools...")
    response = await agent.run(user_msg="What is 25 * 4? Also, what's the weather in New York?")
    print(f"\nFinal response: {response.final_response}")

    trace = agent.telemetry.get_trace_by_run_id(response.run_id)
    if trace:
        print(f"\nTrace information for run: {response.run_id}")
        print(f"  Agent: {trace.agent_name}")
        print(f"  Status: {trace.status}")
        print(f"  Duration: {trace.duration_ms:.2f}ms")
        print(f"  Start time: {trace.start_time}")
        print(f"  End time: {trace.end_time}")
        print(f"  Number of steps: {len(trace.steps)}")

        print("\nTrace steps:")
        for i, step in enumerate(trace.steps, 1):
            print(f"\n  Step {i}: {step.type} at {step.timestamp}")

            if step.type == "tool_call":
                print(f"    Tool: {step.data.get('tool_name', 'unknown')}")
                print(f"    Arguments: {step.data.get('arguments', '{}')}")
            elif step.type == "llm_response":
                print(f"    Model: {step.data.get('model', 'unknown')}")
                print(f"    Has tool calls: {step.data.get('has_tool_calls', False)}")
            elif step.type == "agent_stop":
                if "final_response" in step.data:
                    print(f"    Final response: {step.data['final_response'][:50]}...")
                if "usage" in step.data and step.data["usage"]:
                    print(f"    Token usage: {step.data['usage'].get('tokens', {})}")

        trace_path = Path("./output/traces")
        trace_path.mkdir(parents=True, exist_ok=True)

        json_file = trace_path / f"{response.run_id}.json"
        with open(json_file, "w") as f:
            f.write(trace.to_json())

        print(f"\nTrace exported to: {json_file}")
    else:
        print(f"\n❌ No trace found for run ID: {response.run_id}")

    print("\nRunning another query...")
    response2 = await agent.run(user_msg="What's the weather in London and Tokyo?")
    print(f"\nFinal response: {response2.final_response}")

    traces = agent.telemetry.get_traces_by_agent("TraceAgent")
    print(f"\nFound {len(traces)} traces for TraceAgent")

    for i, trace in enumerate(traces, 1):
        print(f"\n  Trace {i}:")
        print(f"    Run ID: {trace.run_id}")
        print(f"    Status: {trace.status}")
        print(f"    Steps: {len(trace.steps)}")
        print(f"    Duration: {trace.duration_ms:.2f}ms")


if __name__ == "__main__":
    asyncio.run(main())
