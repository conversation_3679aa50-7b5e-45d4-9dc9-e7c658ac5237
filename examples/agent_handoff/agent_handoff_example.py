"""Example demonstrating agent handoff capabilities in the igniteai-agent-studio framework.

This example creates a system with specialized agents and a coordinator agent that:
1. Determines which expert agent is best suited for a task
2. Delegates the task with proper context
3. Handles the response and potential errors
4. Tracks the execution of handoffs
"""

import asyncio
import json
from datetime import datetime

from igniteai_agent_sdk.agent import Agent
from igniteai_agent_sdk.tools.agent_handoff import AgentHandoffEvent, handoff_manager


# Function to answer math questions
async def calculate(expression: str) -> str:
    """Calculate the result of a mathematical expression.

    Args:
        expression: A string representing a mathematical expression

    Returns:
        The result as a string

    """
    try:
        # Using eval for demonstration purposes only - in production, use a safer approach
        result = eval(expression)
        return f"The result of {expression} is {result}"
    except Exception as e:
        return f"Error calculating {expression}: {str(e)}"


# Function to search for information (simulated)
async def search_information(query: str) -> str:
    """Search for information (simulated).

    Args:
        query: Search query

    Returns:
        Simulated search results

    """
    # This is a simplified simulation - in a real scenario, this would query an actual database
    knowledge_base = {
        "python": "Python is a high-level, general-purpose programming language known for its readability.",
        "javascript": "JavaScript is a programming language used primarily for web development.",
        "machine learning": "Machine learning is a subfield of AI focused on building systems that learn from data.",
        "agent": "In AI, an agent is a system that perceives its environment and takes actions to achieve goals.",
        "handoff": "A handoff is the process of transferring a task or control from one entity to another.",
    }

    for keyword, info in knowledge_base.items():
        if keyword.lower() in query.lower():
            return info

    return f"No specific information found for '{query}'."


# Create specialized agents
math_agent = Agent(
    name="Math Expert",
    introduction="You are a mathematics expert who can solve various math problems.",
    role="Mathematics Specialist",
    task="Solve mathematical problems and provide clear step-by-step solutions.",
    verbose=True,
    tools=[calculate],
)

research_agent = Agent(
    name="Research Expert",
    introduction="You are a research specialist who can find and summarize information.",
    role="Research Specialist",
    task="Research topics and provide accurate, concise information.",
    verbose=True,
    tools=[search_information],
)

code_agent = Agent(
    name="Coding Expert",
    introduction="You are a programming expert who can write and explain code.",
    role="Software Developer",
    task="Write efficient, clean code and explain programming concepts clearly.",
    instructions=(
        "Always include comments in your code"
        "Explain your code implementation in detail"
        "Consider performance implications of your solutions"
    ),
    verbose=True,
)

# Create a coordinator agent
coordinator_agent = Agent(
    name="Task Coordinator",
    introduction="You are a smart coordinator who delegates tasks to specialized experts.",
    role="Task Manager",
    task="Analyze user requests and determine which specialized expert should handle the task.",
    instructions=(
        "For mathematical problems or calculations, delegate to the Math Expert."
        "For research questions or general knowledge inquiries, delegate to the Research Expert."
        "For programming questions or code implementation, delegate to the Coding Expert."
        "If a task requires multiple experts, break it down and delegate each part appropriately."
        "Always provide context when delegating tasks."
    ),
    agents=[math_agent, research_agent, code_agent],  # Register sub-agents
    verbose=True,
    memory_enabled=True,  # Enable memory to track interactions across sessions
)


# Set up event tracking for handoffs
handoff_events = []


async def track_handoff_events(event: AgentHandoffEvent):
    """Track agent handoff events."""
    handoff_events.append(
        {
            "timestamp": datetime.now().isoformat(),
            "source_agent": event.source_agent,
            "target_agent": event.target_agent,
            "task": event.task_description,
            "status": event.status,
            "result": event.result if event.result else None,
            "error": event.error if event.error else None,
        }
    )

    # Print real-time status updates
    print(f"[{event.status.upper()}] {event.source_agent} → {event.target_agent}: {event.task_description[:50]}...")


async def main():
    # Subscribe to handoff events for tracking
    coordinator_agent.event_emitter.on(AgentHandoffEvent, track_handoff_events)
    print("=== Agent Handoff Example ===")
    print(f"Available workers: {handoff_manager.get_worker_names()}\n")

    # List of demo queries to test different handoff scenarios
    demo_queries = [
        "I need both a factorial function in JavaScript and the result of 10!",
        "This task should fail because it's impossible: divide 10 by zero and explain the result",
    ]

    for i, query in enumerate(demo_queries, 1):
        print(f"\n--- Query {i}/{len(demo_queries)} ---")
        print(f"User: {query}\n")

        try:
            # Run the coordinator agent with the query
            response = await coordinator_agent.run(user_msg=query)
            print(f"Response: {response.final_response}")
        except Exception as e:
            print(f"Error: {str(e)}")

        print("\n" + "-" * 50)

    # Print summary of handoff events
    print("\n=== Handoff Events Summary ===")
    for i, event in enumerate(handoff_events, 1):
        print(f"{i}. {event['source_agent']} → {event['target_agent']} ({event['status']})")
        if event["result"]:
            print(f"   Result: {event['result'][:100]}..." if len(event["result"]) > 100 else event["result"])
        if event["error"]:
            print(f"   Error: {event['error']}")

    # Save handoff events to a JSON file for analysis
    with open("handoff_events.json", "w") as f:
        json.dump(handoff_events, f, indent=2)
    print("\nHandoff events saved to handoff_events.json")


if __name__ == "__main__":
    asyncio.run(main())
