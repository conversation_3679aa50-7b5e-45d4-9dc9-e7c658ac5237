"""Ignite AI Framework.

A framework for building AI agents and workflows.
"""

from ._agent_context import Context
from ._event_emitter import EventEmitter
from .agent import Agent
from .llm import LLM
from .media import Audio, Image
from .memory import (
    FileMemory,
    Memory,
    MemoryFactory,
    MemoryManager,
)
from .prompts import (
    REACTPROMPT,
    PromptTemplate,
    PromptTemplateManager,
    template_manager,
)
from .telemetry import AgentTrace, Telemetry, TraceStep
from .tools import (
    MCPServer,
    Tool,
    ToolKit,
    get_memory_tools,
    wrap,
)
from .types import (
    Event,
    Response,
    Result,
    StreamChunk,
    StreamResponse,
    Task,
    ToolResponse,
)
from .usage_tracker import TokenUsage, Usage, UsageTracker
from .workflow import Workflow

__version__ = "0.1.0"

__all__ = [
    "AgentTrace",
    "TraceStep",
    "Event",
    "Response",
    "Result",
    "StreamChunk",
    "StreamResponse",
    "Task",
    "ToolResponse",
    "TokenUsage",
    "Usage",
    "UsageTracker",
    "Telemetry",
    "Agent",
    "Memory",
    "FileMemory",
    "MemoryFactory",
    "MemoryManager",
    "REACTPROMPT",
    "PromptTemplate",
    "PromptTemplateManager",
    "template_manager",
    "Tool",
    "ToolKit",
    "wrap",
    "MCPServer",
    "get_memory_tools",
    "Workflow",
    "Context",
    "EventEmitter",
    "LLM",
    "Image",
    "Audio",
]
