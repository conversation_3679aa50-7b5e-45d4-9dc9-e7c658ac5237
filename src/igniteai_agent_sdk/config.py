from pathlib import Path
from typing import Any, Dict, Optional

from pydantic import Field, SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict


class IgniteAISettings(BaseSettings):
    """Settings for Ignite AI framework.

    These settings can be configured via environment variables with the prefix IGNITE_AI_.
    For example, to set the default_model, set the environment variable IGNITE_AI_DEFAULT_MODEL.

    Settings can also be provided via a .env file.
    """

    DEFAULT_MODEL: str = Field(default="openai:gpt-4.1", description="Default model to use for agents")
    LOG_LEVEL: str = Field(default="DEBUG", description="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")

    LLM_API_KEY: SecretStr = Field(default=SecretStr(""), description="API key for LLM provider")
    LLM_BASE_URL: Optional[str] = Field(default=None, description="Base url to access the llm")
    LLM_TEMPERATURE: float = Field(default=0.7, description="Temperature for LLM responses")
    LLM_TOP_P: int = Field(default=1, description="Number of top results to return from LLM")
    LLM_MAX_TOKENS: int = Field(default=8012, description="Maximum number of completions to generate")

    MEMORY_DIR: Path = Field(default=Path("./output/memory"), description="Directory to store memory files")

    WORKFLOW_DIR: Path = Field(default=Path("./output/workflow"), description="Directory to store workflow files")

    model_config = SettingsConfigDict(
        env_file=".ignite-ai.env",
        env_file_encoding="utf-8",
        extra="ignore",
        case_sensitive=False,
    )

    def model_dict(self) -> Dict[str, Any]:
        """Convert settings to a dictionary, handling SecretStr properly."""
        result = {}
        for key, value in self.model_dump().items():
            if isinstance(value, SecretStr):
                result[key] = value.get_secret_value() if value else None
            else:
                result[key] = value
        return result


settings = IgniteAISettings()
