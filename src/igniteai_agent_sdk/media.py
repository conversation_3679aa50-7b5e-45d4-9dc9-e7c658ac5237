import base64
from enum import Enum
from pathlib import Path
from typing import Optional, Union
from urllib.parse import urlparse

import aiofiles
import aiohttp
from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator


class MediaType(str, Enum):
    """Supported media types."""

    IMAGE = "image"
    AUDIO = "audio"


class Media(BaseModel):
    """A robust media class that supports both Image and Audio types.

    Accepts either a URL or file_path and automatically converts the content
    to base64 encoding with proper MIME type detection.

    Attributes:
        media_type: The type of media (image or audio)
        url: Optional URL to fetch media from
        file_path: Optional local file path to read media from
        content: The base64 encoded media data (auto-populated)
        mime_type: The MIME type of the media (auto-detected)
        size_bytes: Size of the media in bytes (auto-populated)

    """

    media_type: MediaType
    url: Optional[str] = Field(default=None, description="URL to fetch media from")
    file_path: Optional[Union[str, Path]] = Field(default=None, description="Local file path to read media from")
    content: Optional[str] = Field(default=None, description="Base64 encoded media data")
    mime_type: Optional[str] = Field(default=None, description="MIME type of the media")
    size_bytes: Optional[int] = Field(default=None, description="Size of the media in bytes")

    max_file_size: int = Field(default=50 * 1024 * 1024, description="Maximum file size in bytes (50MB)")
    timeout_seconds: int = Field(default=30, description="HTTP request timeout in seconds")

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @field_validator("file_path", mode="before")
    @classmethod
    def validate_file_path(cls, v):
        """Convert string paths to Path objects."""
        if isinstance(v, str):
            return Path(v)
        return v

    @model_validator(mode="after")
    def validate_source(self):
        """Ensure exactly one of url or file_path is provided."""
        if not self.url and not self.file_path:
            raise ValueError("Either 'url' or 'file_path' must be provided")
        if self.url and self.file_path:
            raise ValueError("Only one of 'url' or 'file_path' should be provided")
        return self

    @field_validator("url")
    @classmethod
    def validate_url(cls, v):
        """Validate URL format."""
        if v is not None:
            parsed = urlparse(v)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid URL format")
        return v

    async def load(self) -> "Media":
        """Load and process the media data.

        Returns:
            Self with populated content, mime_type, and size_bytes

        Raises:
            FileNotFoundError: If file_path doesn't exist
            ValueError: If file is too large or unsupported type
            aiohttp.ClientError: If URL fetch fails

        """
        if self.content:
            return self

        if self.file_path:
            await self._load_from_file()
        elif self.url:
            await self._load_from_url()

        return self

    async def _load_from_file(self) -> None:
        """Load media from local file path."""
        if not self.file_path or not self.file_path.exists():  # type: ignore
            raise FileNotFoundError(f"File not found: {self.file_path}")

        file_size = self.file_path.stat().st_size  # type: ignore
        if file_size > self.max_file_size:
            raise ValueError(f"File too large: {file_size} bytes (max: {self.max_file_size})")

        mime_type = self._get_mime_type_from_extension(self.file_path.suffix)  # type: ignore

        self._validate_mime_type(mime_type)

        async with aiofiles.open(self.file_path, "rb") as file:
            content = await file.read()

        self.content = base64.b64encode(content).decode("utf-8")
        self.mime_type = mime_type
        self.size_bytes = len(content)

    async def _load_from_url(self) -> None:
        """Load media from URL."""
        timeout = aiohttp.ClientTimeout(total=self.timeout_seconds)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(self.url) as response:  # type: ignore
                response.raise_for_status()

                content_length = response.headers.get("content-length")
                if content_length and int(content_length) > self.max_file_size:
                    raise ValueError(f"Content too large: {content_length} bytes (max: {self.max_file_size})")

                mime_type = response.headers.get("content-type", "").split(";")[0]
                if not mime_type:
                    parsed_url = urlparse(self.url)
                    path = Path(parsed_url.path)  # type: ignore
                    mime_type = self._get_mime_type_from_extension(path.suffix)

                self._validate_mime_type(mime_type)

                content = await response.read()

                if len(content) > self.max_file_size:
                    raise ValueError(f"Content too large: {len(content)} bytes (max: {self.max_file_size})")

                self.content = base64.b64encode(content).decode("utf-8")
                self.mime_type = mime_type
                self.size_bytes = len(content)

    def _get_mime_type_from_extension(self, extension: str) -> str:
        """Get MIME type from file extension."""
        extension = extension.lower()

        image_types = {
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".png": "image/png",
            ".gif": "image/gif",
            ".webp": "image/webp",
        }

        audio_types = {
            ".mp3": "audio/mpeg",
            ".wav": "audio/wav",
        }

        if self.media_type == MediaType.IMAGE:
            return image_types.get(extension, "application/octet-stream")
        elif self.media_type == MediaType.AUDIO:
            return audio_types.get(extension, "application/octet-stream")

        return "application/octet-stream"

    def _validate_mime_type(self, mime_type: str) -> None:
        """Validate that MIME type matches the expected media type."""
        if not mime_type:
            return

        mime_type = mime_type.lower()

        if self.media_type == MediaType.IMAGE and not mime_type.startswith("image/"):
            raise ValueError(f"Expected image MIME type, got: {mime_type}")
        elif self.media_type == MediaType.AUDIO and not mime_type.startswith("audio/"):
            raise ValueError(f"Expected audio MIME type, got: {mime_type}")

    def get_data_url(self) -> str:
        """Get the media as a data URL.

        Returns:
            Data URL string (e.g., "data:image/jpeg;base64,/9j/4AAQ...")

        Raises:
            ValueError: If media is not loaded

        """
        if not self.content:
            raise ValueError("Media not loaded. Call load() first.")

        mime_type = self.mime_type or "application/octet-stream"
        return f"data:{mime_type};base64,{self.content}"

    def save_to_file(self, file_path: Union[str, Path]) -> None:
        """Save the media data to a file.

        Args:
            file_path: Path where to save the file

        Raises:
            ValueError: If media is not loaded

        """
        if not self.content:
            raise ValueError("Media not loaded. Call load() first.")

        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)

        content = base64.b64decode(self.content)
        with open(file_path, "wb") as file:
            file.write(content)

    @classmethod
    async def from_file(cls, file_path: Union[str, Path], media_type: MediaType, **kwargs) -> "Media":
        """Create and load Media from a file path.

        Args:
            file_path: Path to the media file
            media_type: Type of media (image or audio)
            **kwargs: Additional configuration options

        Returns:
            Loaded Media instance

        """
        media = cls(media_type=media_type, file_path=file_path, **kwargs)
        await media.load()
        return media

    @classmethod
    async def from_url(cls, url: str, media_type: MediaType, **kwargs) -> "Media":
        """Create and load Media from a URL.

        Args:
            url: URL to the media file
            media_type: Type of media (image or audio)
            **kwargs: Additional configuration options

        Returns:
            Loaded Media instance

        """
        media = cls(media_type=media_type, url=url, **kwargs)
        await media.load()
        return media

    def __str__(self) -> str:
        """String representation of the Media object."""
        source = self.url or str(self.file_path) if self.file_path else "unknown"
        loaded = "loaded" if self.content else "not loaded"
        size = f" ({self.size_bytes} bytes)" if self.size_bytes else ""
        return f"Media({self.media_type.value}, {source}, {loaded}{size})"

    def __repr__(self) -> str:
        """Detailed representation of the Media object."""
        return (
            f"Media("
            f"media_type={self.media_type.value!r}, "
            f"url={self.url!r}, "
            f"file_path={self.file_path!r}, "
            f"mime_type={self.mime_type!r}, "
            f"size_bytes={self.size_bytes}, "
            f"loaded={bool(self.content)}"
            f")"
        )


class Image(Media):
    """Convenience class for image media."""

    def __init__(self, **kwargs):
        super().__init__(media_type=MediaType.IMAGE, **kwargs)


class Audio(Media):
    """Convenience class for audio media."""

    def __init__(self, **kwargs):
        super().__init__(media_type=MediaType.AUDIO, **kwargs)
