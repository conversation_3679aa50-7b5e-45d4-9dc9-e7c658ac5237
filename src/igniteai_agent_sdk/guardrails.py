import functools
from typing import TYPE_CHECKING, Any, Awaitable, Callable, Generic, Optional, TypeVar

if TYPE_CHECKING:
    from igniteai_agent_sdk.agent import Agent


T = TypeVar("T")
AgentType = TypeVar("AgentType", bound="Agent")


class GuardrailTripwireError(Exception):
    """Exception raised when a guardrail tripwire is triggered."""

    def __init__(self, message: str, guardrail_name: str, output_info: Optional[Any] = None):
        super().__init__(message)
        self.guardrail_name = guardrail_name
        self.output_info = output_info


class InputGuardrailTripwireError(GuardrailTripwireError):
    """Exception raised when an input guardrail tripwire is triggered."""

    pass


class OutputGuardrailTripwireError(GuardrailTripwireError):
    """Exception raised when an output guardrail tripwire is triggered."""

    pass


class GuardrailFunctionOutput(Generic[T]):
    """Output from a guardrail function."""

    def __init__(self, output_info: Optional[T] = None, tripwire_triggered: bool = False, message: Optional[str] = None):
        self.output_info = output_info
        self.tripwire_triggered = tripwire_triggered
        self.message = message or "Guardrail tripwire triggered"


GuardrailFunction = Callable[..., Awaitable[GuardrailFunctionOutput]]


class Guardrail:
    """A guardrail that can be applied to agents."""

    def __init__(self, guardrail_function: GuardrailFunction, name: Optional[str] = None, description: Optional[str] = None):
        self.function = guardrail_function
        self.name = name or guardrail_function.__name__
        self.description = description or guardrail_function.__doc__ or "No description provided"


def input_guardrail(func: Optional[Callable] = None, *, name: Optional[str] = None, description: Optional[str] = None) -> Any:
    """Decorator to create an input guardrail function.

    Args:
        func: The function to convert into a guardrail.
        name: Optional name for the guardrail.
        description: Optional description for the guardrail.

    Returns:
        A decorator function or Guardrail instance.

    """

    def decorator(f: Callable) -> Guardrail:
        @functools.wraps(f)
        async def wrapper(agent: Any, input_message: str, **kwargs: Any) -> GuardrailFunctionOutput:
            result = await f(agent, input_message, **kwargs)
            if result.tripwire_triggered:
                raise InputGuardrailTripwireError(
                    message=result.message, guardrail_name=name or f.__name__, output_info=result.output_info
                )
            return result

        return Guardrail(wrapper, name=name or f.__name__, description=description or f.__doc__)

    if func is None:
        return decorator
    else:
        return decorator(func)


def output_guardrail(func: Optional[Callable] = None, *, name: Optional[str] = None, description: Optional[str] = None) -> Any:
    """Decorator to create an output guardrail function.

    Args:
        func: The function to convert into a guardrail.
        name: Optional name for the guardrail.
        description: Optional description for the guardrail.

    Returns:
        A decorator function or Guardrail instance.

    """

    def decorator(f: Callable) -> Guardrail:
        @functools.wraps(f)
        async def wrapper(agent: Any, output: Any, **kwargs: Any) -> GuardrailFunctionOutput:
            result = await f(agent, output, **kwargs)
            if result.tripwire_triggered:
                raise OutputGuardrailTripwireError(
                    message=result.message, guardrail_name=name or f.__name__, output_info=result.output_info
                )
            return result

        return Guardrail(wrapper, name=name or f.__name__, description=description or f.__doc__)

    if func is None:
        return decorator
    else:
        return decorator(func)
