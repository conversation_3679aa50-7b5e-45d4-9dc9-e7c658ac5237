import importlib
from abc import ABC, abstractmethod
from typing import Any, AsyncGenerator, Dict, List, Optional, Sequence, Union

from igniteai_agent_sdk.llm.types import ChatCompletionMessage as Message
from igniteai_agent_sdk.llm.types import ChatCompletionResponse
from igniteai_agent_sdk.types import Stream<PERSON>hunk
from igniteai_agent_sdk.usage_tracker import UsageTracker


class LLMError(Exception):
    """Custom exception for LLM errors."""

    def __init__(self, message):
        super().__init__(message)


class Provider(ABC):
    @abstractmethod
    def chat_completions_create(self, model, messages, **kwargs) -> ChatCompletionResponse:
        """Abstract method for chat completion calls, to be implemented by each provider."""
        pass

    @abstractmethod
    async def achat_completions_create(self, model, messages, **kwargs) -> ChatCompletionResponse:
        """Abstract async method for chat completion calls, to be implemented by each provider."""
        pass

    @abstractmethod
    def achat_completions_stream(self, model, messages, **kwargs) -> AsyncGenerator[StreamChunk, None]:
        """Abstract method for streaming chat completion calls, to be implemented by each provider."""
        pass


class LLM:
    def __init__(self, provider_name: str, provider_configs: Optional[Dict] = None):
        """Initialize the client with provider configurations."""
        if provider_configs is None:
            provider_configs = {}

        self.provider_name = provider_name
        self.provider_configs = provider_configs
        self.client: Provider = self._init_provider()

        # Initialize retry counter
        self._retry_attempt = 0

    def _init_provider(self):
        provider_class_name = f"{self.provider_name.capitalize()}Client"
        provider_module_name = f"{self.provider_name}_client"

        module_path = f"igniteai_agent_sdk.llm.providers.{provider_module_name}"

        try:
            module = importlib.import_module(module_path)
        except ImportError as e:
            raise ImportError(
                f"Could not import module {module_path}: {str(e)}. Provider {self.provider_name} not yet supported."
            ) from e

        provider_class = getattr(module, provider_class_name)
        return provider_class(**self.provider_configs)

    def _extract_thinking_content(self, response):
        """Extract content between <think> tags if present and store it in reasoning_content.

        Args:
            response: The response object from the provider

        Returns:
            Modified response object

        """
        if hasattr(response, "choices") and response.choices:
            message = response.choices[0].message
            if hasattr(message, "content") and message.content:
                content = message.content.strip()
                if content.startswith("<think>") and "</think>" in content:
                    # Extract content between think tags
                    start_idx = len("<think>")
                    end_idx = content.find("</think>")
                    thinking_content = content[start_idx:end_idx].strip()

                    # Store the thinking content
                    message.reasoning = thinking_content

                    # Remove the think tags from the original content
                    message.content = content[end_idx + len("</think>") :].strip()

        return response

    def complete(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        **model_run_params,
    ):
        """Perform a completion request with automatic retries.

        Args:
            model: The model to use for completion
            messages: The messages to send to the model
            tools: The tools to use for completion
            **model_run_params: Additional parameters for the model

        Returns:
            A ChatCompletionResponse with the model's response

        """
        try:
            response = self.client.chat_completions_create(model, messages, tools=tools, **model_run_params)

            return self._extract_thinking_content(response)

        except Exception as e:
            raise LLMError(f"LLM request failed: {str(e)}") from e

    async def acomplete(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[List] = None,
        usage_tracker: Optional[UsageTracker] = None,
        **model_run_params,
    ):
        """Perform an asynchronous completion request with automatic retries.

        Args:
            model: The model to use for completion
            messages: The messages to send to the model
            tools: The tools to use for completion
            usage_tracker: The usage tracker to use for tracking usage
            **model_run_params: Additional parameters for the model

        Returns:
            A ChatCompletionResponse with the model's response

        """
        try:
            if usage_tracker:
                usage_tracker.start_llm_call(model=model)
            response = await self.client.achat_completions_create(model, messages, tools=tools, **model_run_params)
            if usage_tracker and hasattr(response, "usage"):
                usage = getattr(response, "usage", None)
                if usage:
                    prompt_tokens = getattr(usage, "prompt_tokens", 0)
                    completion_tokens = getattr(usage, "completion_tokens", 0)
                    usage_tracker.update_token_usage(prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)
            return self._extract_thinking_content(response)

        except Exception as e:
            raise LLMError(f"Async LLM request failed: {str(e)}") from e

    async def astream(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[List] = None,
        usage_tracker: Optional[UsageTracker] = None,
        **model_run_params,
    ) -> AsyncGenerator[StreamChunk, None]:
        """Perform an asynchronous streaming completion request.

        Args:
            model: The model to use for completion
            messages: The messages to send to the model
            tools: The tools to use for completion
            usage_tracker: The usage tracker to use for tracking usage
            **model_run_params: Additional parameters for the model

        Yields:
            StreamChunk objects containing incremental response data

        """
        try:
            if usage_tracker:
                usage_tracker.start_llm_call(model=model)

            chunk_index = 0
            stream = self.client.achat_completions_stream(model, messages, tools=tools, **model_run_params)
            async for chunk in stream:
                chunk.chunk_index = chunk_index
                chunk_index += 1
                yield chunk

        except Exception as e:
            raise LLMError(f"Async streaming LLM request failed: {str(e)}") from e
