import json
import uuid
from time import time
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field

from igniteai_agent_sdk.media import Audio, Image


class Function(BaseModel):
    arguments: str | dict | None = None
    name: str | None = None


class ChatCompletionMessageToolCall(BaseModel):
    id: str = f"call_id{str(uuid.uuid4())[:8]}"
    function: Function = Field(default=Function())
    type: Literal["function"] = "function"


class AudioResponse(BaseModel):
    id: Optional[str] = None
    data: Optional[str] = None
    expires_at: Optional[int] = None
    transcript: Optional[str] = None


class ChatCompletionMessage(BaseModel):
    """Model for messages."""

    role: Optional[Literal["user", "assistant", "system", "tool"]] = None
    content: str | None = None
    audio: Optional[AudioResponse] = None
    image_inputs: Optional[List[Image]] = None
    audio_inputs: Optional[List[Audio]] = None
    reasoning: Optional[str] = None
    name: str | None = None
    tool_calls: Optional[List[ChatCompletionMessageToolCall]] = None
    tool_call_id: str | None = None
    tool_name: str | None = None
    tool_args: Any | None = Field(default=None)
    tool_error: bool = False
    created_at: int = Field(default_factory=lambda: int(time()))

    model_config = ConfigDict(extra="ignore")

    def get_content(self) -> Optional[str]:
        """Returns the content as a string."""
        if isinstance(self.content, str):
            return self.content
        if isinstance(self.content, list):
            return json.dumps(self.content)
        return None

    def to_dict(self) -> Dict[str, Any]:
        model_dict = self.model_dump(
            exclude_none=True,
            exclude={
                "reasoning",
                "created_at",
                "tool_error",
                "tool_args",
                "tool_name",
                "image_inputs",
                "audio_inputs",
                "audio_response",
            },
        )
        model_dict["content"] = self.get_content()

        return model_dict


class CompletionUsage(BaseModel):
    completion_tokens: int = 0
    prompt_tokens: int = 0
    total_tokens: int = 0


class Choice(BaseModel):
    message: ChatCompletionMessage = Field(default=ChatCompletionMessage())
    finish_reason: Literal["stop", "length", "tool_calls", "content_filter", "function_call", "error"] = Field(default="stop")
    index: Optional[int] = 0

    model_config = ConfigDict(extra="allow")


class RetryInfo(BaseModel):
    """Information about retries that occurred during the request."""

    attempts: int = 0
    success: bool = True
    last_error: Optional[str] = None
    retry_after: Optional[float] = None


class ChatCompletionResponse(BaseModel):
    """Used to conform to the response model of OpenAI."""

    choices: list[Choice] = Field(default=[Choice()])
    model: str | None = None
    usage: CompletionUsage = Field(default=CompletionUsage())
    id: Optional[str] = Field(default_factory=lambda: f"chatcmpl-{str(uuid.uuid4())}")
    created: Optional[int] = None
    object: str = "chat.completion"
    retry_info: Optional[RetryInfo] = None

    model_config = ConfigDict(extra="allow")
