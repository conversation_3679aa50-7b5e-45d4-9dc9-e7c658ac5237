import os
from typing import Any, AsyncGenerator, Dict, Optional, Sequence, Union

import groq

from igniteai_agent_sdk.config import settings
from igniteai_agent_sdk.llm.client import LLMError, Provider
from igniteai_agent_sdk.llm.types import ChatCompletionMessage as Message
from igniteai_agent_sdk.llm.types import ChatCompletionMessageToolCall, ChatCompletionResponse
from igniteai_agent_sdk.types import StreamChunk


class GroqClient(Provider):
    def __init__(self, **config):
        if config.get("api_key", None) is None:
            config["api_key"] = settings.LLM_API_KEY.get_secret_value() or os.getenv("GROQ_API_KEY")
        if not config["api_key"]:
            raise ValueError(
                "Groq API key is missing. Please provide it in the config or set the GROQ_API_KEY environment variable."
            )

        self.client = groq.Groq(**config)
        self.async_client = groq.AsyncGroq(**config)

    def format_messages(self, messages: Sequence[Union[Message, Dict[str, Any]]]):
        """Convert messages to Groq-compatible format."""
        transformed_messages = []
        for message in messages:
            if isinstance(message, Message):
                message_dict = message.to_dict()

                if message.role == "user":
                    text_content = message_dict.pop("content")
                    message_dict["content"] = [{"type": "text", "text": text_content}]
                    if message.image_inputs is not None and message.image_inputs:
                        message_dict["content"].extend(
                            [{"type": "image_url", "image_url": {"url": image.get_data_url()}} for image in message.image_inputs]
                        )

                    if message.audio_inputs is not None and message.audio_inputs:
                        raise ValueError("Audio inputs are not yet supported when using Groq Models.")
            else:
                message_dict = Message.model_validate(message).to_dict()

            if message_dict["role"] == "tool":
                message_dict["content"] = str(message_dict["content"])

            transformed_messages.append(message_dict)
        return transformed_messages

    def chat_completions_create(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        stream: bool = False,
        **kwargs,
    ):
        try:
            transformed_messages = self.format_messages(messages)
            response = self.client.chat.completions.create(
                model=model,
                messages=transformed_messages,
                tools=tools,  # type: ignore
                stream=stream,
                **kwargs,
            )
            return self._normalize_response(response)
        except Exception as e:
            raise LLMError(f"An error occurred: {e}") from e

    async def achat_completions_create(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        stream: bool = False,
        **kwargs,
    ):
        try:
            transformed_messages = self.format_messages(messages)
            response = await self.async_client.chat.completions.create(
                model=model,
                messages=transformed_messages,
                tools=tools,  # type: ignore
                stream=stream,
                **kwargs,
            )
            return self._normalize_response(response)
        except Exception as e:
            raise LLMError(f"An error occurred during async completion: {e}") from e

    def _normalize_response(self, response) -> ChatCompletionResponse:
        """Normalize the API response to a common format (ChatCompletionResponse)."""
        normalized_response = ChatCompletionResponse()
        normalized_response.model = response.model
        normalized_response.choices[0].message.content = response.choices[0].message.content
        normalized_response.choices[0].message.role = response.choices[0].message.role
        normalized_response.choices[0].finish_reason = response.choices[0].finish_reason
        normalized_response.usage.prompt_tokens = response.usage.prompt_tokens
        normalized_response.usage.completion_tokens = response.usage.completion_tokens
        normalized_response.usage.total_tokens = response.usage.total_tokens
        if response.choices[0].message.tool_calls is not None:
            normalized_response.choices[0].message.tool_calls = []
            for _tool_call in response.choices[0].message.tool_calls:
                tool = ChatCompletionMessageToolCall()
                tool.function.name = _tool_call.function.name
                tool.function.arguments = _tool_call.function.arguments
                normalized_response.choices[0].message.tool_calls.append(tool)
            normalized_response.choices[0].finish_reason = "tool_calls"

        return normalized_response

    def achat_completions_stream(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream chat completions from Groq API."""

        async def _stream():
            try:
                transformed_messages = self.format_messages(messages)
                stream = await self.async_client.chat.completions.create(
                    model=model,
                    messages=transformed_messages,
                    tools=tools,  # type: ignore
                    stream=True,
                    **kwargs,
                )

                accumulated_content = ""
                chunk_index = 0
                accumulated_tool_calls = []

                async for chunk in stream:
                    if not chunk.choices:
                        continue

                    choice = chunk.choices[0]
                    delta = choice.delta

                    content_delta = None
                    if hasattr(delta, "content") and delta.content:
                        content_delta = delta.content
                        accumulated_content += content_delta

                    if hasattr(delta, "tool_calls") and delta.tool_calls:
                        for tool_call_delta in delta.tool_calls:
                            if hasattr(tool_call_delta, "index"):
                                index = tool_call_delta.index

                                while len(accumulated_tool_calls) <= index:
                                    accumulated_tool_calls.append(
                                        {"id": "", "type": "function", "function": {"name": "", "arguments": ""}}
                                    )

                                if hasattr(tool_call_delta, "id") and tool_call_delta.id:
                                    accumulated_tool_calls[index]["id"] = tool_call_delta.id
                                if hasattr(tool_call_delta, "function") and tool_call_delta.function:
                                    if hasattr(tool_call_delta.function, "name") and tool_call_delta.function.name:
                                        accumulated_tool_calls[index]["function"]["name"] += tool_call_delta.function.name
                                    if hasattr(tool_call_delta.function, "arguments") and tool_call_delta.function.arguments:
                                        accumulated_tool_calls[index]["function"]["arguments"] += (
                                            tool_call_delta.function.arguments
                                        )

                    finish_reason = choice.finish_reason if hasattr(choice, "finish_reason") else None
                    is_final = finish_reason is not None

                    yield StreamChunk(
                        content=accumulated_content if accumulated_content else None,
                        delta=content_delta,
                        finish_reason=finish_reason,
                        tool_calls=accumulated_tool_calls if accumulated_tool_calls else None,
                        chunk_index=chunk_index,
                        is_final=is_final,
                    )

                    chunk_index += 1

            except Exception as e:
                raise LLMError(f"An error occurred during streaming: {e}") from e

        return _stream()
