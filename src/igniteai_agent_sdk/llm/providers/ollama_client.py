import os
from typing import Any, AsyncGenerator, Dict, Optional, Sequence, Union

from ollama import AsyncClient, Client

from igniteai_agent_sdk.config import settings
from igniteai_agent_sdk.llm.client import LLMError, Provider
from igniteai_agent_sdk.llm.types import ChatCompletionMessage as Message
from igniteai_agent_sdk.llm.types import ChatCompletionMessageToolCall, ChatCompletionResponse
from igniteai_agent_sdk.types import StreamChunk


class OllamaClient(Provider):
    def __init__(self, **config):
        """Initialize the Ollama provider with the given configuration."""
        host = settings.LLM_BASE_URL or os.getenv("OLLAMA_API_URL", "http://localhost:11434")
        if config.get("api_url", None) is not None:
            host = config.pop("api_url")

        self.client = Client(host=host, **config)
        self.aclient = AsyncClient(host=host, **config)

    def format_messages(self, messages: Sequence[Union[Message, Dict[str, Any]]]):
        """Convert messages to OpenAI-compatible format."""
        transformed_messages = []
        for message in messages:
            if isinstance(message, Message):
                message_dict = message.to_dict()

                if message.role == "user":
                    if message.image_inputs is not None and message.image_inputs:
                        message_dict["images"] = [image.get_data_url() for image in message.image_inputs]

                    if message.audio_inputs is not None and message.audio_inputs:
                        raise ValueError("Audio inputs are not yet supported when using Ollama Models.")
            else:
                message_dict = Message.model_validate(message).to_dict()

            if message_dict["role"] == "tool":
                message_dict["content"] = str(message_dict["content"])

            transformed_messages.append(message_dict)
        return transformed_messages

    def transform_model_params(self, run_params: Dict[str, Any]) -> Dict[str, Any]:
        """Transform model parameters for Ollama."""
        run_params["num_ctx"] = run_params.get("max_completion_tokens", 4096)
        return run_params

    def chat_completions_create(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        stream: bool = False,
        **kwargs,
    ) -> ChatCompletionResponse:
        """Makes a request to the chat completions endpoint using httpx."""
        transformed_messages = self.format_messages(messages)
        run_params = self.transform_model_params(kwargs)
        try:
            response = self.client.chat(
                model=model,
                messages=transformed_messages,
                tools=tools,  # type: ignore
                stream=stream,  # type: ignore
                options=run_params,
            )

        except Exception as e:
            raise LLMError(f"An error occurred: {e}") from e

        return self._normalize_response(response)

    async def achat_completions_create(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        stream: bool = False,
        **kwargs,
    ) -> ChatCompletionResponse:
        """Makes a request to the chat completions endpoint using httpx."""
        transformed_messages = self.format_messages(messages)
        run_params = self.transform_model_params(kwargs)
        try:
            response = await self.aclient.chat(
                model=model,
                messages=transformed_messages,
                tools=tools,  # type: ignore
                stream=stream,  # type: ignore
                options=run_params,
            )

        except Exception as e:
            raise LLMError(f"An error occurred: {e}") from e

        return self._normalize_response(response)

    def _normalize_response(self, response) -> ChatCompletionResponse:
        """Normalize the API response to a common format (ChatCompletionResponse)."""
        normalized_response = ChatCompletionResponse()
        normalized_response.model = response.model
        normalized_response.choices[0].message.content = response.message.content
        normalized_response.choices[0].message.role = response.message.role
        normalized_response.choices[0].finish_reason = response.done_reason
        normalized_response.usage.prompt_tokens = response.prompt_eval_count
        normalized_response.usage.completion_tokens = response.eval_count
        normalized_response.usage.total_tokens = response.prompt_eval_count + response.eval_count
        if response.message.tool_calls is not None:
            normalized_response.choices[0].message.tool_calls = []
            for _tool_call in response.message.tool_calls:
                tool = ChatCompletionMessageToolCall()
                tool.function.name = _tool_call.function.name
                tool.function.arguments = _tool_call.function.arguments
                normalized_response.choices[0].message.tool_calls.append(tool)
            normalized_response.choices[0].finish_reason = "tool_calls"

        return normalized_response

    def achat_completions_stream(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream chat completions from Ollama API."""

        async def _stream():
            try:
                transformed_messages = self.format_messages(messages)
                run_params = self.transform_model_params(kwargs)

                stream = await self.aclient.chat(
                    model=model,
                    messages=transformed_messages,
                    tools=tools,  # type: ignore
                    stream=True,
                    options=run_params,
                )

                accumulated_content = ""
                chunk_index = 0

                async for chunk in stream:
                    content_delta = None
                    if hasattr(chunk, "message") and hasattr(chunk.message, "content") and chunk.message.content:
                        content_delta = chunk.message.content
                        accumulated_content += content_delta

                    tool_calls = None
                    if hasattr(chunk, "message") and hasattr(chunk.message, "tool_calls") and chunk.message.tool_calls:
                        tool_calls = []
                        for tool_call in chunk.message.tool_calls:
                            tool_calls.append(
                                {
                                    "id": getattr(tool_call, "id", f"call_{chunk_index}"),
                                    "type": "function",
                                    "function": {
                                        "name": tool_call.function.name,
                                        "arguments": tool_call.function.arguments,
                                    },
                                }
                            )

                    finish_reason = None
                    is_final = False
                    if hasattr(chunk, "done") and chunk.done:
                        finish_reason = getattr(chunk, "done_reason", "stop")
                        is_final = True

                    yield StreamChunk(
                        content=accumulated_content if accumulated_content else None,
                        delta=content_delta,
                        finish_reason=finish_reason,
                        tool_calls=tool_calls,
                        chunk_index=chunk_index,
                        is_final=is_final,
                    )

                    chunk_index += 1

            except Exception as e:
                raise LLMError(f"An error occurred during streaming: {e}") from e

        return _stream()
