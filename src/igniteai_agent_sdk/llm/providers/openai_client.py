import os
from typing import Any, Async<PERSON>enerator, Dict, Optional, Sequence, Union

import openai
from openai import AsyncOpenAI

from igniteai_agent_sdk.config import settings
from igniteai_agent_sdk.llm.client import LLMError, Provider
from igniteai_agent_sdk.llm.types import ChatCompletionMessage as Message
from igniteai_agent_sdk.types import StreamChunk


class OpenaiClient(Provider):
    def __init__(self, **config):
        if config.get("api_key", None) is None:
            config["api_key"] = settings.LLM_API_KEY.get_secret_value() or os.getenv("OPENAI_API_KEY")

        self.client = openai.OpenAI(**config)
        self.async_client = AsyncOpenAI(**config)

    def format_messages(self, messages: Sequence[Union[Message, Dict[str, Any]]]):
        """Convert messages to OpenAI-compatible format."""
        transformed_messages = []
        for message in messages:
            if isinstance(message, Message):
                message_dict = message.to_dict()

                if message.role == "user":
                    text_content = message_dict.pop("content")
                    message_dict["content"] = [{"type": "text", "text": text_content}]
                    if message.image_inputs is not None and message.image_inputs:
                        message_dict["content"].extend(
                            [{"type": "image_url", "image_url": {"url": image.get_data_url()}} for image in message.image_inputs]
                        )

                    if message.audio_inputs is not None and message.audio_inputs:
                        message_dict["content"].extend(
                            [
                                {
                                    "type": "input_audio",
                                    "input_audio": {"data": audio.content, "format": audio.mime_type.split("/")[1]},  # type: ignore
                                }
                                for audio in message.audio_inputs
                            ]
                        )
            elif isinstance(message, Dict):
                message_dict = Message.model_validate(message).to_dict()
            else:
                message_dict = Message.model_validate(message.model_dump()).to_dict()

            if message_dict["role"] == "tool":
                message_dict["content"] = str(message_dict["content"])

            transformed_messages.append(message_dict)
        return transformed_messages

    def chat_completions_create(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        stream: bool = False,
        **kwargs,
    ):
        try:
            transformed_messages = self.format_messages(messages)
            response = self.client.chat.completions.create(
                model=model,
                messages=transformed_messages,
                tools=tools,  # type: ignore
                stream=stream,
                **kwargs,
            )
            return response
        except Exception as e:
            raise LLMError(f"An error occurred: {e}") from e

    async def achat_completions_create(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        stream: bool = False,
        **kwargs,
    ):
        try:
            transformed_messages = self.format_messages(messages)

            response = await self.async_client.chat.completions.create(
                model=model,
                messages=transformed_messages,
                tools=tools,  # type: ignore
                stream=stream,
                **kwargs,
            )

            return response
        except Exception as e:
            raise LLMError(f"An error occurred during async completion: {e}") from e

    def achat_completions_stream(
        self,
        model: str,
        messages: Sequence[Union[Message, Dict[str, Any]]],
        tools: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream chat completions from OpenAI API."""

        async def _stream():
            try:
                transformed_messages = self.format_messages(messages)
                stream = await self.async_client.chat.completions.create(
                    model=model,
                    messages=transformed_messages,
                    tools=tools,  # type: ignore
                    stream=True,
                    stream_options={"include_usage": True},
                    **kwargs,
                )

                accumulated_content = ""
                chunk_index = 0
                accumulated_tool_calls = []

                async for chunk in stream:
                    if not chunk.choices:
                        continue

                    choice = chunk.choices[0]
                    delta = choice.delta
                    # TODO: Need to handle usage metrics
                    content_delta = None
                    if hasattr(delta, "content") and delta.content:
                        content_delta = delta.content
                        accumulated_content += content_delta

                    if hasattr(delta, "tool_calls") and delta.tool_calls:
                        for tool_call_delta in delta.tool_calls:
                            if hasattr(tool_call_delta, "index"):
                                index = tool_call_delta.index

                                while len(accumulated_tool_calls) <= index:
                                    accumulated_tool_calls.append(
                                        {"id": "", "type": "function", "function": {"name": "", "arguments": ""}}
                                    )

                                if hasattr(tool_call_delta, "id") and tool_call_delta.id:
                                    accumulated_tool_calls[index]["id"] = tool_call_delta.id
                                if hasattr(tool_call_delta, "function") and tool_call_delta.function:
                                    if hasattr(tool_call_delta.function, "name") and tool_call_delta.function.name:
                                        accumulated_tool_calls[index]["function"]["name"] += tool_call_delta.function.name
                                    if hasattr(tool_call_delta.function, "arguments") and tool_call_delta.function.arguments:
                                        accumulated_tool_calls[index]["function"]["arguments"] += (
                                            tool_call_delta.function.arguments
                                        )

                    finish_reason = choice.finish_reason if hasattr(choice, "finish_reason") else None
                    is_final = finish_reason is not None

                    yield StreamChunk(
                        content=accumulated_content if accumulated_content else None,
                        delta=content_delta,
                        finish_reason=finish_reason,
                        tool_calls=accumulated_tool_calls if accumulated_tool_calls else None,
                        chunk_index=chunk_index,
                        is_final=is_final,
                    )

                    chunk_index += 1

            except Exception as e:
                raise LLMError(f"An error occurred during streaming: {e}") from e

        return _stream()
