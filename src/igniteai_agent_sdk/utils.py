import json
from datetime import datetime
from typing import List, Optional, Type, TypeVar, Union

from pydantic import BaseModel

from igniteai_agent_sdk.logger import root_logger

T = TypeVar("T")


def debug_print(debug: bool, *args: str) -> None:
    if not debug:
        return
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    message = " ".join(map(str, args))
    root_logger.debug(f"\033[97m[\033[90m{timestamp}\033[97m]\033[90m {message}\033[0m")


def merge_fields(target, source):
    for key, value in source.items():
        if isinstance(value, str):
            target[key] += value
        elif value is not None and isinstance(value, dict):
            merge_fields(target[key], value)


async def interactive_chat_loop(
    agent,
    exit_commands: Optional[List[str]] = None,
    welcome_message: Optional[str] = None,
    prompt_prefix: str = "You: ",
    max_turns: int = 10,
):
    """Simulate an interactive chat loop between a user and an agent.

    Args:
        agent: An instance of Agent or a compatible class that implements run()
        exit_commands: List of strings that will exit the chat loop (e.g. ["exit", "quit", "bye"])
        welcome_message: Optional message to display at the beginning
        prompt_prefix: Text to display before user input
        max_turns: Maximum number of turns in the conversation before automatic exit

    Example:
        ```python
        agent = ReActAgent(model="openai/gpt-4o", name="Assistant")
        await interactive_chat_loop(agent, welcome_message="Hello! I'm your AI assistant. How can I help?")
        ```

    """
    print("\033[95m" + "=" * 50 + "\033[0m")

    if welcome_message:
        print(f"\033[94mSystem: {welcome_message}\033[0m")

    if exit_commands is None:
        exit_commands = ["exit", "quit", "bye", "/exit", "/quit", "/bye"]

    if not hasattr(agent, "run"):
        raise ValueError("Agent must implement a 'run' method")

    history = []
    turn_count = 0

    while True:
        turn_count += 1
        user_input = input(f"\033[92m{prompt_prefix}\033[0m ")

        if user_input.lower().strip() in exit_commands:
            print("\033[94mSystem: Ending chat session.\033[0m")
            break

        print("\033[94mProcessing...\033[0m")
        try:
            print(f"\033[96m{agent.name}:\033[0m ", end="", flush=True)

            response = await agent.run(user_msg=user_input, history=history, max_turns=max_turns)
            print(response.final_response)
            if response:
                history.extend(
                    [{"role": "user", "content": user_input}, {"role": "assistant", "content": response.final_response}]
                )

        except Exception as e:
            print(f"\033[91mError: {str(e)}\033[0m")

    print("\033[95m" + "=" * 50 + "\033[0m")
    print("\033[94mChat session ended.\033[0m")


def get_json_schema(response_model: Union[BaseModel, Type[BaseModel]]) -> str:
    json_output_prompt = "Provide your output as a JSON containing the following fields:\n"
    if (isinstance(response_model, type) and issubclass(response_model, BaseModel)) or isinstance(response_model, BaseModel):
        json_schema = response_model.model_json_schema()
        if json_schema is not None:
            response_model_properties = {}
            json_schema_properties = json_schema.get("properties")
            if json_schema_properties is not None:
                for field_name, field_properties in json_schema_properties.items():
                    formatted_field_properties = {
                        prop_name: prop_value for prop_name, prop_value in field_properties.items() if prop_name != "title"
                    }
                    # Handle enum references
                    if "allOf" in formatted_field_properties:
                        ref = formatted_field_properties["allOf"][0].get("$ref", "")
                        if ref.startswith("#/$defs/"):
                            enum_name = ref.split("/")[-1]
                            formatted_field_properties["enum_type"] = enum_name

                    response_model_properties[field_name] = formatted_field_properties

            json_schema_defs = json_schema.get("$defs")
            if json_schema_defs is not None:
                response_model_properties["$defs"] = {}
                for def_name, def_properties in json_schema_defs.items():
                    if "enum" in def_properties:
                        response_model_properties["$defs"][def_name] = {
                            "type": "string",
                            "enum": def_properties["enum"],
                            "description": def_properties.get("description", ""),
                        }
                    else:
                        def_fields = def_properties.get("properties")
                        formatted_def_properties = {}
                        if def_fields is not None:
                            for field_name, field_properties in def_fields.items():
                                formatted_field_properties = {
                                    prop_name: prop_value
                                    for prop_name, prop_value in field_properties.items()
                                    if prop_name != "title"
                                }
                                formatted_def_properties[field_name] = formatted_field_properties
                        if len(formatted_def_properties) > 0:
                            response_model_properties["$defs"][def_name] = formatted_def_properties

            if len(response_model_properties) > 0:
                json_output_prompt += "\n<json_fields>"
                json_output_prompt += f"\n{json.dumps([key for key in response_model_properties.keys() if key != '$defs'])}"
                json_output_prompt += "\n</json_fields>"
                json_output_prompt += "\n\nHere are the properties for each field:"
                json_output_prompt += "\n<json_field_properties>"
                json_output_prompt += f"\n{json.dumps(response_model_properties, indent=2)}"
                json_output_prompt += "\n</json_field_properties>"

    json_output_prompt += "\nStart your response with `{` and end it with `}`."
    json_output_prompt += "\nYour output will be passed to json.loads() to convert it to a Python object."
    json_output_prompt += "\nMake sure it only contains valid JSON."
    return json_output_prompt
