from __future__ import annotations

import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Union
from uuid import uuid4

from pydantic import BaseModel, ConfigDict, Field, field_serializer

from igniteai_agent_sdk.types import (
    AgentErrorEvent,
    AgentStartEvent,
    AgentStopEvent,
    LLMResponseEvent,
    ToolCallEvent,
)


class TraceStep(BaseModel):
    """Represents a single step in an agent execution trace."""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    id: str = Field(default_factory=lambda: str(uuid4()))
    timestamp: datetime = Field(default_factory=datetime.now)
    type: str
    agent_name: str
    run_id: str
    data: Dict[str, Any] = Field(default_factory=dict)

    @field_serializer("timestamp")
    def serialize_timestamp(self, timestamp: datetime) -> str:
        return timestamp.isoformat()


class AgentTrace(BaseModel):
    """Complete execution trace for an agent run."""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    id: str = Field(default_factory=lambda: str(uuid4()))
    agent_name: str
    run_id: str
    start_time: datetime = Field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    status: str = "running"
    steps: List[TraceStep] = Field(default_factory=list)
    error: Optional[str] = None
    duration_ms: float = 0
    metadata: Dict[str, Any] = Field(default_factory=dict)

    @field_serializer("start_time", "end_time")
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        if dt is None:
            return None
        return dt.isoformat()

    def get_duration_ms(self) -> float:
        """Calculate the duration of the trace in milliseconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds() * 1000
        return 0

    def to_dict(self) -> Dict[str, Any]:
        """Convert the trace to a dictionary format."""
        return {
            "id": self.id,
            "agent_name": self.agent_name,
            "run_id": self.run_id,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "status": self.status,
            "steps": [step.model_dump() for step in self.steps],
            "error": self.error,
            "duration_ms": self.duration_ms,
            "metadata": self.metadata,
        }

    def to_json(self, indent: int = 2) -> str:
        """Convert the trace to a JSON string."""
        return json.dumps(self.to_dict(), indent=indent)


class Telemetry:
    """Telemetry manager for tracking agent execution traces."""

    def __init__(self, max_traces: int = 100, agent_name: str = "unknown"):
        """Initialize the telemetry manager.

        Args:
            max_traces: Maximum number of traces to keep in memory
            agent_name: Default agent name to use when not specified

        """
        self.traces: Dict[str, AgentTrace] = {}
        self.active_runs: Set[str] = set()
        self.max_traces = max_traces
        self.default_agent_name = agent_name

    def subscribe_to_agent(self, agent) -> None:
        """Subscribe to events from an agent.

        Args:
            agent: The agent to subscribe to

        """
        agent.event_emitter.on(AgentStartEvent, self._handle_agent_start)
        agent.event_emitter.on(AgentStopEvent, self._handle_agent_stop)
        agent.event_emitter.on(AgentErrorEvent, self._handle_agent_error)
        agent.event_emitter.on(LLMResponseEvent, self._handle_llm_response)
        agent.event_emitter.on(ToolCallEvent, self._handle_tool_call)

    def unsubscribe_from_agent(self, agent) -> None:
        """Unsubscribe from events from an agent.

        Args:
            agent: The agent to unsubscribe from

        """
        agent.event_emitter.off(AgentStartEvent, self._handle_agent_start)
        agent.event_emitter.off(AgentStopEvent, self._handle_agent_stop)
        agent.event_emitter.off(AgentErrorEvent, self._handle_agent_error)
        agent.event_emitter.off(LLMResponseEvent, self._handle_llm_response)
        agent.event_emitter.off(ToolCallEvent, self._handle_tool_call)

    def create_trace(self, agent_name: str, run_id: str, metadata: Optional[Dict[str, Any]] = None) -> AgentTrace:
        """Create a new trace for an agent run.

        Args:
            agent_name: Name of the agent
            run_id: ID of the run
            metadata: Optional metadata to include with the trace

        Returns:
            The created trace

        """
        if len(self.traces) >= self.max_traces:
            oldest = None
            oldest_time = None

            for trace_id, trace in self.traces.items():
                if trace.run_id not in self.active_runs:
                    if oldest_time is None or trace.start_time < oldest_time:
                        oldest = trace_id
                        oldest_time = trace.start_time

            if oldest:
                self.traces.pop(oldest)

        trace = AgentTrace(
            agent_name=agent_name,
            run_id=run_id,
            metadata=metadata or {},
        )
        self.traces[trace.id] = trace
        self.active_runs.add(run_id)
        return trace

    def record_tool_call(self, tool_name: str, arguments: str, run_id: Optional[str] = None) -> None:
        """Record a tool call in the active trace.

        Args:
            tool_name: Name of the tool
            arguments: Arguments passed to the tool
            run_id: Optional run ID (if None, will try to find the most recent active trace)

        """
        if not run_id and self.active_runs:
            active_traces = []
            for trace in self.traces.values():
                if trace.run_id in self.active_runs:
                    active_traces.append(trace)

            if active_traces:
                active_traces.sort(key=lambda t: t.start_time, reverse=True)
                run_id = active_traces[0].run_id

        if run_id:
            self.add_trace_step(
                run_id=run_id,
                step_type="tool_call",
                agent_name=self.default_agent_name,
                data={"tool_name": tool_name, "arguments": arguments},
            )

    def add_trace_step(
        self, run_id: str, step_type: str, agent_name: Optional[str] = None, data: Optional[Dict[str, Any]] = None
    ) -> Optional[TraceStep]:
        """Add a step to an agent's trace.

        Args:
            run_id: ID of the run
            step_type: Type of the step
            agent_name: Name of the agent (defaults to the instance default)
            data: Optional data to include with the step

        Returns:
            The created trace step or None if the run is not being traced

        """
        trace = self.get_trace_by_run_id(run_id)
        if not trace:
            return None

        step = TraceStep(
            type=step_type,
            agent_name=agent_name or self.default_agent_name,
            run_id=run_id,
            data=data or {},
        )

        trace.steps.append(step)
        return step

    def complete_trace(self, run_id: str, status: str = "completed", error: Optional[str] = None) -> Optional[AgentTrace]:
        """Mark a trace as completed.

        Args:
            run_id: ID of the run to complete
            status: Final status of the run
            error: Optional error message if the run failed

        Returns:
            The updated trace or None if the run is not being traced

        """
        trace = self.get_trace_by_run_id(run_id)
        if not trace:
            return None

        trace.status = status
        trace.end_time = datetime.now()
        trace.duration_ms = trace.get_duration_ms()

        if error:
            trace.error = error

        self.active_runs.discard(run_id)
        return trace

    def get_trace_by_id(self, trace_id: str) -> Optional[AgentTrace]:
        """Get a trace by its ID.

        Args:
            trace_id: ID of the trace

        Returns:
            The trace or None if not found

        """
        return self.traces.get(trace_id)

    def get_trace_by_run_id(self, run_id: str) -> Optional[AgentTrace]:
        """Get a trace by its run ID.

        Args:
            run_id: ID of the run

        Returns:
            The trace or None if not found

        """
        for trace in self.traces.values():
            if trace.run_id == run_id:
                return trace
        return None

    def get_traces_by_agent(self, agent_name: str) -> List[AgentTrace]:
        """Get all traces for a specific agent.

        Args:
            agent_name: Name of the agent

        Returns:
            List of traces

        """
        return [trace for trace in self.traces.values() if trace.agent_name == agent_name]

    def export_trace(self, trace_id: str, format: str = "json") -> Union[str, Dict[str, Any]]:
        """Export a trace in the specified format.

        Args:
            trace_id: ID of the trace
            format: Format to export as ("json" or "dict")

        Returns:
            The exported trace

        Raises:
            ValueError: If the trace is not found or the format is invalid

        """
        trace = self.get_trace_by_id(trace_id)
        if not trace:
            raise ValueError(f"Trace with ID {trace_id} not found")

        if format == "json":
            return trace.to_json()
        elif format == "dict":
            return trace.to_dict()
        else:
            raise ValueError(f"Invalid export format: {format}")

    def clear_traces(self, only_completed: bool = True) -> int:
        """Clear traces from memory.

        Args:
            only_completed: If True, only clear completed traces

        Returns:
            Number of traces cleared

        """
        if only_completed:
            completed_traces = [trace_id for trace_id, trace in self.traces.items() if trace.run_id not in self.active_runs]
            for trace_id in completed_traces:
                self.traces.pop(trace_id)
            return len(completed_traces)
        else:
            count = len(self.traces)
            self.traces.clear()
            self.active_runs.clear()
            return count

    def _handle_agent_start(self, event: AgentStartEvent) -> None:
        """Handle agent start event."""
        agent_name = event.agent_name or self.default_agent_name
        run_id = event.run_id

        if not run_id:
            return

        self.create_trace(agent_name, run_id)

    def _handle_agent_stop(self, event: AgentStopEvent) -> None:
        """Handle agent stop event."""
        run_id = event.run_id

        if not run_id:
            return

        response_data = {}
        if event.response:
            if hasattr(event.response, "final_response"):
                response_data["final_response"] = event.response.final_response
            if hasattr(event.response, "usage"):
                response_data["usage"] = event.response.usage.model_dump() if event.response.usage else None

        self.add_trace_step(run_id, "agent_stop", event.agent_name or self.default_agent_name, response_data)
        self.complete_trace(run_id)

    def _handle_agent_error(self, event: AgentErrorEvent) -> None:
        """Handle agent error event."""
        agent_name = getattr(event, "agent_name", self.default_agent_name)
        agent_traces = self.get_traces_by_agent(agent_name)
        active_traces = [t for t in agent_traces if t.run_id in self.active_runs]

        if active_traces:
            active_traces.sort(key=lambda t: t.start_time, reverse=True)
            run_id = active_traces[0].run_id

            self.add_trace_step(run_id, "agent_error", agent_name, {"error": event.error})
            self.complete_trace(run_id, status="failed", error=event.error)

    def _handle_llm_response(self, event: LLMResponseEvent) -> None:
        """Handle LLM response event."""
        run_id = getattr(event, "run_id", None)
        if not run_id:
            if self.active_runs:
                active_traces = []
                for trace in self.traces.values():
                    if trace.run_id in self.active_runs:
                        active_traces.append(trace)

                if active_traces:
                    active_traces.sort(key=lambda t: t.start_time, reverse=True)
                    run_id = active_traces[0].run_id

        if run_id:
            agent_name = getattr(event, "agent_name", self.default_agent_name)
            data = {
                "model": event.model,
                "content": event.content,
                "has_tool_calls": bool(event.tool_calls),
            }
            self.add_trace_step(run_id, "llm_response", agent_name, data)

    def _handle_tool_call(self, event: ToolCallEvent) -> None:
        """Handle tool call event."""
        run_id = getattr(event, "run_id", None)
        if run_id:
            agent_name = getattr(event, "agent_name", self.default_agent_name)
            data = {
                "tool_name": getattr(event, "tool_name", "unknown"),
                "arguments": getattr(event, "arguments", "{}"),
            }
            self.add_trace_step(run_id, "tool_call", agent_name, data)
