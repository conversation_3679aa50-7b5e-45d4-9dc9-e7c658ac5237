from datetime import datetime
from typing import Any, Dict, List, Optional, Set

from igniteai_agent_sdk.memory.base_memory import Memory
from igniteai_agent_sdk.memory.memory_factory import MemoryFactory
from igniteai_agent_sdk.utils import debug_print


class MemoryManager:
    """Manager for agent memory operations."""

    def __init__(self, memory: Optional[Memory] = None, memory_type: str = "file", verbose: bool = False, **memory_kwargs):
        """Initialize the memory manager.

        Args:
            memory: An existing Memory instance (if provided)
            memory_type: Type of memory to create if none provided
            verbose: Enable verbose logging
            **memory_kwargs: Arguments for memory creation if needed

        """
        self.verbose = verbose
        self.memory = memory if memory else MemoryFactory.create_memory(memory_type=memory_type, verbose=verbose, **memory_kwargs)
        self.current_run_id = None
        debug_print(self.verbose, f"MemoryManager initialized with {memory_type} memory")

    def set_run_id(self, run_id: str) -> None:
        """Set the current run ID for memory operations.

        Args:
            run_id: The run ID to use

        """
        self.current_run_id = run_id
        debug_print(self.verbose, f"MemoryManager set current run ID: {run_id}")

    def clear_run_id(self) -> None:
        """Clear the current run ID."""
        self.current_run_id = None
        debug_print(self.verbose, "MemoryManager cleared current run ID")

    async def remember(
        self, content: Any, key: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None, run_id: Optional[str] = None
    ) -> str:
        """Store information in memory.

        Args:
            content: Content to remember
            key: Optional key for the memory entry
            metadata: Optional metadata for the memory entry
            run_id: Optional run ID (defaults to current run ID if set)

        Returns:
            The key of the stored memory

        """
        if key is None:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            key = f"memory_{timestamp}"

        if metadata is None:
            metadata = {}

        metadata["stored_at"] = datetime.now().isoformat()

        memory_run_id = run_id if run_id is not None else self.current_run_id

        await self.memory.add(key, content, metadata, memory_run_id)
        debug_print(self.verbose, f"Remembered content with key: {key}" + (f" for run: {memory_run_id}" if memory_run_id else ""))
        return key

    async def recall(self, key: str) -> Optional[Any]:
        """Retrieve a specific memory by key.

        Args:
            key: The key to retrieve

        Returns:
            The memory value or None if not found

        """
        entry = await self.memory.get(key)
        if entry:
            debug_print(self.verbose, f"Recalled memory with key: {key}")
            return entry["value"]
        debug_print(self.verbose, f"No memory found for key: {key}")
        return None

    async def recall_by_query(self, query: str, limit: int = 5, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search memory by query.

        Args:
            query: The search query
            limit: Maximum number of results
            run_id: Optional run ID to filter by (defaults to current run ID if set)

        Returns:
            List of matching memory entries

        """
        results = await self.memory.search(query, limit, run_id)

        debug_print(self.verbose, f"Recall by query '{query}' returned {len(results)} results")
        return results

    async def recall_by_run(self, run_id: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Retrieve memories by run ID.

        Args:
            run_id: The run ID to retrieve memories for (defaults to current run ID if set)
            limit: Maximum number of memories to retrieve

        Returns:
            List of memory entries for the specified run

        """
        memory_run_id = run_id if run_id is not None else self.current_run_id

        if not memory_run_id:
            debug_print(self.verbose, "No run ID specified or set for recall_by_run")
            return []

        results = await self.memory.get_by_run_id(memory_run_id, limit)
        debug_print(self.verbose, f"Recall by run returned {len(results)} results for run: {memory_run_id}")
        return results

    async def get_run_ids(self) -> Set[str]:
        """Get all unique run IDs in the memory.

        Returns:
            Set of unique run IDs

        """
        return await self.memory.get_run_ids()

    async def recall_recent(self, limit: int = 5, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Retrieve recent memories.

        Args:
            limit: Maximum number of memories to retrieve
            run_id: Optional run ID to filter by (defaults to current run ID if set)

        Returns:
            List of recent memory entries

        """
        memory_run_id = run_id if run_id is not None else self.current_run_id

        return await self.memory.get_recent(limit, memory_run_id)

    async def forget(self, key: str) -> bool:
        """Delete a specific memory.

        Args:
            key: Key of the memory to delete

        Returns:
            True if deleted, False if not found

        """
        return await self.memory.delete(key)

    async def forget_run(self, run_id: Optional[str] = None) -> int:
        """Delete all memories for a specific run.

        Args:
            run_id: Run ID to delete memories for (defaults to current run ID if set)

        Returns:
            Number of memories deleted

        """
        memory_run_id = run_id if run_id is not None else self.current_run_id

        if not memory_run_id:
            debug_print(self.verbose, "No run ID specified or set for forget_run")
            return 0

        deleted_count = await self.memory.delete_by_run_id(memory_run_id)
        debug_print(self.verbose, f"Deleted {deleted_count} memories for run: {memory_run_id}")
        return deleted_count

    async def update_memory(
        self, key: str, content: Optional[Any] = None, metadata: Optional[Dict[str, Any]] = None, run_id: Optional[str] = None
    ) -> bool:
        """Update an existing memory.

        Args:
            key: Key of the memory to update
            content: New content (None to keep existing)
            metadata: New metadata (None to keep existing)
            run_id: Optional run ID to associate with the memory

        Returns:
            True if updated, False if not found

        """
        memory_run_id = run_id if run_id is not None else self.current_run_id

        return await self.memory.update(key, content, metadata, memory_run_id)

    async def clear_all(self) -> None:
        """Clear all memories."""
        await self.memory.clear()
        debug_print(self.verbose, "Cleared all memories")
