from pathlib import Path
from typing import Optional, Union

from igniteai_agent_sdk.memory.base_memory import Memory
from igniteai_agent_sdk.memory.file_memory import FileMemory


class MemoryFactory:
    """Factory for creating memory instances."""

    @staticmethod
    def create_memory(
        memory_type: str = "file",
        directory: Optional[Union[str, Path]] = None,
        filename: str = "memory.json",
        verbose: bool = False,
        **kwargs,
    ) -> Memory:
        """Create a memory instance of the specified type.

        Args:
            memory_type: Type of memory to create ('file' is currently supported)
            directory: Directory for file-based memory
            filename: Filename for file-based memory
            verbose: Enable verbose logging
            **kwargs: Additional arguments for specific memory types

        Returns:
            An instance of Memory

        Raises:
            ValueError: If memory_type is not supported

        """
        if memory_type == "file":
            if directory is None:
                directory = "./output/memory"
            return FileMemory(directory=directory, filename=filename, verbose=verbose)
        else:
            raise ValueError(f"Unsupported memory type: {memory_type}")
