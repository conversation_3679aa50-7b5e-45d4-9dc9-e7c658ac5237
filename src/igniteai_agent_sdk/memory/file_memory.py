import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

import aiofiles

from igniteai_agent_sdk.memory.base_memory import Memory
from igniteai_agent_sdk.utils import debug_print


class FileMemory(Memory):
    """File-based memory implementation."""

    def __init__(self, directory: Union[str, Path], filename: str = "memory.json", verbose: bool = False):
        """Initialize file-based memory.

        Args:
            directory: Directory to store memory file
            filename: Name of the memory file
            verbose: Enable verbose logging

        """
        self.directory = Path(directory)
        self.filename = filename
        self.filepath = self.directory / self.filename
        self.verbose = verbose

        self.directory.mkdir(parents=True, exist_ok=True)

        if not self.filepath.exists():
            with open(self.filepath, "w") as f:
                json.dump({}, f)

        debug_print(self.verbose, f"FileMemory initialized at {self.filepath}")

    async def _read_memory_file(self) -> Dict[str, Any]:
        """Read the memory file contents."""
        try:
            async with aiofiles.open(self.filepath, "r") as f:
                content = await f.read()
                return json.loads(content) if content else {}
        except json.JSONDecodeError:
            debug_print(self.verbose, "Error reading memory file, initializing new memory")
            return {}

    async def _write_memory_file(self, data: Dict[str, Any]) -> None:
        """Write data to the memory file."""
        async with aiofiles.open(self.filepath, "w") as f:
            await f.write(json.dumps(data, indent=2))

    async def add(self, key: str, value: Any, metadata: Optional[Dict[str, Any]] = None, run_id: Optional[str] = None) -> None:
        data = await self._read_memory_file()

        if not key:
            key = str(uuid.uuid4())

        if key in data:
            key = f"{key}_{str(uuid.uuid4())[:8]}"

        timestamp = datetime.now().isoformat()

        if metadata is None:
            metadata = {}

        if run_id:
            metadata["run_id"] = run_id

        entry = {"value": value, "created_at": timestamp, "updated_at": timestamp, "metadata": metadata}

        data[key] = entry
        await self._write_memory_file(data)
        debug_print(self.verbose, f"Memory entry added with key: {key}" + (f" for run: {run_id}" if run_id else ""))

    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        data = await self._read_memory_file()
        entry = data.get(key)
        if entry:
            debug_print(self.verbose, f"Memory entry retrieved for key: {key}")
            return {"key": key, **entry}
        debug_print(self.verbose, f"No memory entry found for key: {key}")
        return None

    async def search(self, query: str, limit: int = 5, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search memory entries based on a query string.

        Args:
            query: The search query
            limit: Maximum number of results to return
            run_id: Optional run ID to filter results

        Returns:
            List of matching memory entries sorted by relevance

        """
        if not query:
            return await self.get_recent(limit, run_id)

        data = await self._read_memory_file()

        query = query.lower().strip()
        query_terms = set(query.split())

        candidates = {}

        for key, entry in data.items():
            if run_id is not None:
                metadata = entry.get("metadata", {})
                if metadata.get("run_id") != run_id:
                    continue

            value_str = str(entry["value"]).lower()
            key_lower = key.lower()
            metadata_str = str(entry.get("metadata", {})).lower()

            if (
                query in value_str
                or query in key_lower
                or any(term in value_str or term in key_lower or term in metadata_str for term in query_terms)
            ):
                candidates[key] = {
                    "entry": entry,
                    "key": key,
                    "value_str": value_str,
                    "key_lower": key_lower,
                    "metadata_str": metadata_str,
                }

        if not candidates:
            debug_print(self.verbose, f"Search for '{query}' returned 0 results" + (f" for run: {run_id}" if run_id else ""))
            return []

        scored_results = []
        for key, candidate in candidates.items():
            score = 0
            entry = candidate["entry"]
            value_str = candidate["value_str"]
            key_lower = candidate["key_lower"]
            metadata_str = candidate["metadata_str"]

            if query == value_str:
                score += 100
            elif query in value_str:
                score += 40

                if any([value_str.startswith(f"{query} ") or value_str.endswith(f" {query}")]):
                    score += 20

            if query in key_lower:
                score += 30

            value_terms = set(value_str.split())

            matching_terms = query_terms.intersection(value_terms)
            if matching_terms:
                match_ratio = len(matching_terms) / len(query_terms)
                score += 30 * match_ratio

            if any(term in metadata_str for term in query_terms):
                score += 20

            if query in value_str:
                position = value_str.find(query)
                position_score = max(0, 15 - (position / 20))
                score += position_score

            try:
                timestamp = datetime.fromisoformat(entry["updated_at"])

                days_old = (datetime.now() - timestamp).days
                recency_score = max(0, 10 * (1 - min(1, days_old / 30)))
                score += recency_score
            except (ValueError, KeyError):
                pass

            if score > 0:
                scored_results.append((score, {"key": key, **entry}))

        scored_results.sort(reverse=True, key=lambda x: x[0])
        results = [item[1] for item in scored_results[:limit]]

        debug_print(
            self.verbose, f"Search for '{query}' returned {len(results)} results" + (f" for run: {run_id}" if run_id else "")
        )
        return results

    async def get_by_run_id(self, run_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Retrieve memory entries by run ID.

        Args:
            run_id: The run ID to filter by
            limit: Maximum number of entries to retrieve

        Returns:
            List of memory entries for the specified run

        """
        data = await self._read_memory_file()
        results = []

        for key, entry in data.items():
            metadata = entry.get("metadata", {})
            if metadata.get("run_id") == run_id:
                results.append({"key": key, **entry})

        results = sorted(results, key=lambda x: x["updated_at"], reverse=True)[:limit]

        debug_print(self.verbose, f"Retrieved {len(results)} memory entries for run: {run_id}")
        return results

    async def get_run_ids(self) -> Set[str]:
        """Get all unique run IDs in the memory.

        Returns:
            Set of unique run IDs

        """
        data = await self._read_memory_file()
        run_ids = set()

        for entry in data.values():
            metadata = entry.get("metadata", {})
            run_id = metadata.get("run_id")
            if run_id:
                run_ids.add(run_id)

        return run_ids

    async def get_recent(self, limit: int = 5, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        data = await self._read_memory_file()
        entries = []

        for k, v in data.items():
            if run_id is not None:
                metadata = v.get("metadata", {})
                if metadata.get("run_id") != run_id:
                    continue
            entries.append({"key": k, **v})

        sorted_entries = sorted(entries, key=lambda x: x["updated_at"], reverse=True)[:limit]

        debug_print(
            self.verbose, f"Retrieved {len(sorted_entries)} recent memory entries" + (f" for run: {run_id}" if run_id else "")
        )
        return sorted_entries

    async def update(
        self, key: str, value: Any = None, metadata: Optional[Dict[str, Any]] = None, run_id: Optional[str] = None
    ) -> bool:
        data = await self._read_memory_file()

        if key not in data:
            debug_print(self.verbose, f"Update failed: No memory entry found for key: {key}")
            return False

        entry = data[key]

        if value is not None:
            entry["value"] = value

        if metadata:
            entry["metadata"] = {**entry.get("metadata", {}), **metadata}

        if run_id and entry["metadata"].get("run_id") is None:
            entry["metadata"]["run_id"] = run_id

        entry["updated_at"] = datetime.now().isoformat()

        await self._write_memory_file(data)
        debug_print(self.verbose, f"Memory entry updated for key: {key}")
        return True

    async def delete(self, key: str) -> bool:
        data = await self._read_memory_file()

        if key not in data:
            debug_print(self.verbose, f"Delete failed: No memory entry found for key: {key}")
            return False

        del data[key]
        await self._write_memory_file(data)
        debug_print(self.verbose, f"Memory entry deleted for key: {key}")
        return True

    async def delete_by_run_id(self, run_id: str) -> int:
        """Delete all memory entries for a specific run ID.

        Args:
            run_id: The run ID to delete entries for

        Returns:
            Number of entries deleted

        """
        data = await self._read_memory_file()
        keys_to_delete = []

        for key, entry in data.items():
            metadata = entry.get("metadata", {})
            if metadata.get("run_id") == run_id:
                keys_to_delete.append(key)

        for key in keys_to_delete:
            del data[key]

        await self._write_memory_file(data)

        debug_print(self.verbose, f"Deleted {len(keys_to_delete)} memory entries for run: {run_id}")
        return len(keys_to_delete)

    async def clear(self) -> None:
        await self._write_memory_file({})
        debug_print(self.verbose, "Memory cleared")
