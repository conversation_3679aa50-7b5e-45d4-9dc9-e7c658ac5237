from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set


class Memory(ABC):
    """Abstract base class for agent memory implementations."""

    @abstractmethod
    async def add(self, key: str, value: Any, metadata: Optional[Dict[str, Any]] = None, run_id: Optional[str] = None) -> None:
        """Add a memory entry.

        Args:
            key: Unique identifier for the memory entry
            value: The content to store
            metadata: Optional metadata for the memory entry
            run_id: Optional ID of the agent run this memory belongs to

        """
        pass

    @abstractmethod
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Retrieve a specific memory entry by key.

        Args:
            key: The key to retrieve

        Returns:
            The memory entry or None if not found

        """
        pass

    @abstractmethod
    async def search(self, query: str, limit: int = 5, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search memory entries based on a query string.

        Args:
            query: The search query
            limit: Maximum number of results to return
            run_id: Optional run ID to filter results

        Returns:
            List of matching memory entries

        """
        pass

    @abstractmethod
    async def get_by_run_id(self, run_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Retrieve memory entries by run ID.

        Args:
            run_id: The run ID to filter by
            limit: Maximum number of entries to retrieve

        Returns:
            List of memory entries for the specified run

        """
        pass

    @abstractmethod
    async def get_run_ids(self) -> Set[str]:
        """Get all unique run IDs in the memory.

        Returns:
            Set of unique run IDs

        """
        pass

    @abstractmethod
    async def get_recent(self, limit: int = 5, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Retrieve recent memory entries.

        Args:
            limit: Maximum number of entries to retrieve
            run_id: Optional run ID to filter results

        Returns:
            List of recent memory entries

        """
        pass

    @abstractmethod
    async def update(
        self, key: str, value: Any = None, metadata: Optional[Dict[str, Any]] = None, run_id: Optional[str] = None
    ) -> bool:
        """Update an existing memory entry.

        Args:
            key: The key of the entry to update
            value: New value (or None to keep existing)
            metadata: New metadata (or None to keep existing)
            run_id: Optional run ID to associate with the memory

        Returns:
            True if the entry was updated, False if not found

        """
        pass

    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete a memory entry.

        Args:
            key: The key to delete

        Returns:
            True if the entry was deleted, False if not found

        """
        pass

    @abstractmethod
    async def delete_by_run_id(self, run_id: str) -> int:
        """Delete all memory entries for a specific run ID.

        Args:
            run_id: The run ID to delete entries for

        Returns:
            Number of entries deleted

        """
        pass

    @abstractmethod
    async def clear(self) -> None:
        """Clear all memory entries."""
        pass
