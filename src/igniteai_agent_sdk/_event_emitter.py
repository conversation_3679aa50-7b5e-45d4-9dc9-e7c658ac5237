import inspect
from collections import defaultdict
from typing import Any, Callable, Dict, List, TypeVar, Union

from igniteai_agent_sdk.types import Event

T = TypeVar("T")
EventHandler = Callable[[Any], Union[None, Any]]


class EventEmitter:
    """Event management system for registering, removing, and triggering event handlers.

    Supports both synchronous and asynchronous event handlers, with methods to
    register permanent or one-time event listeners. Works with both string event names
    and Event class types.
    """

    def __init__(self):
        """Initialize an empty event emitter with no registered listeners."""
        self._listeners: Dict[str, List[EventHandler]] = defaultdict(list)
        self._once_listeners: Dict[str, List[EventHandler]] = defaultdict(list)

    def _get_event_name(self, event: type[Event]) -> str:
        """Extract the event name string from either a string or Event class.

        Args:
            event: Event class

        Returns:
            The event name as a string

        """
        if hasattr(event, "__name__"):
            return event.__name__
        return str(event)

    def on(self, event: type[Event], listener: <PERSON>Hand<PERSON>) -> None:
        """Register a listener for a specific event.

        Args:
            event: Event class to listen for
            listener: Callback function to execute when event is emitted

        """
        event_name = self._get_event_name(event)
        self._listeners[event_name].append(listener)

    def once(self, event: type[Event], listener: EventHandler) -> None:
        """Register a one-time listener for a specific event.

        The listener will be automatically removed after being called once.

        Args:
            event: Event class to listen for
            listener: Callback function to execute when event is emitted

        """
        event_name = self._get_event_name(event)
        self._once_listeners[event_name].append(listener)

    def off(self, event: type[Event], listener: EventHandler) -> None:
        """Remove a listener from a specific event.

        Args:
            event: Event class to remove listener from
            listener: Callback function to remove

        """
        event_name = self._get_event_name(event)

        if event_name in self._listeners and listener in self._listeners[event_name]:
            self._listeners[event_name].remove(listener)

        if event_name in self._once_listeners and listener in self._once_listeners[event_name]:
            self._once_listeners[event_name].remove(listener)

    async def emit(self, event: Event) -> bool:
        """Emit an event and call all registered listeners with the provided data.

        Args:
            event: Event instance

        Returns:
            True if any listeners were called, False otherwise

        """
        event_name = self._get_event_name(event.__class__)

        called_any = False

        if event_name in self._listeners:
            for listener in self._listeners[event_name]:
                called_any = True
                if inspect.iscoroutinefunction(listener):
                    await listener(event)
                else:
                    listener(event)

        if event_name in self._once_listeners and self._once_listeners[event_name]:
            once_handlers = self._once_listeners[event_name].copy()

            self._once_listeners[event_name] = []

            for handler in once_handlers:
                called_any = True
                if inspect.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)

        return called_any
