import json
import uuid
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """Status of a task in a workflow."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskExecution(BaseModel):
    """Tracks the execution of a specific task."""

    task_id: str
    execution_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    status: TaskStatus = TaskStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    event_data: Optional[Dict[str, Any]] = None
    result: Optional[Any] = None
    error: Optional[str] = None

    def start(self, event_data: Optional[Dict[str, Any]] = None) -> None:
        """Mark the task as started."""
        self.status = TaskStatus.RUNNING
        self.start_time = datetime.now()
        self.event_data = event_data

    def complete(self, result: Any = None) -> None:
        """Mark the task as completed successfully."""
        self.status = TaskStatus.COMPLETED
        self.end_time = datetime.now()
        self.result = result

    def fail(self, error: str) -> None:
        """Mark the task as failed."""
        self.status = TaskStatus.FAILED
        self.end_time = datetime.now()
        self.error = error

    @property
    def duration(self) -> Optional[float]:
        """Calculate the duration of the task execution in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


class WorkflowExecution(BaseModel):
    """Tracks the execution of a workflow."""

    workflow_id: str
    execution_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    task_executions: Dict[str, List[TaskExecution]] = Field(default_factory=dict)
    initial_data: Optional[Dict[str, Any]] = None
    result: Optional[Any] = None
    storage_dir: str = Field(default="./output/workflow_executions")
    status: str = "created"

    def start(self, initial_data: Optional[Dict[str, Any]] = None) -> None:
        """Mark the workflow as started."""
        self.status = "running"
        self.start_time = datetime.now()
        self.initial_data = initial_data
        self.save_to_file()

    def complete(self, result: Any = None) -> None:
        """Mark the workflow as completed."""
        self.status = "completed"
        self.end_time = datetime.now()
        self.result = result
        self.save_to_file()

    def fail(self, error: str) -> None:
        """Mark the workflow as failed."""
        self.status = "failed"
        self.end_time = datetime.now()
        self.result = {"error": error}
        self.save_to_file()

    def register_task_execution(self, task_id: str) -> TaskExecution:
        """Register a new task execution."""
        if task_id not in self.task_executions:
            self.task_executions[task_id] = []

        execution = TaskExecution(task_id=task_id)
        self.task_executions[task_id].append(execution)
        self.save_to_file()
        return execution

    def save_to_file(self, file_path: Optional[str] = None) -> str:
        """Save the execution details to a file.

        Args:
            file_path: Optional specific file path to save to

        Returns:
            Path where the file was saved

        """
        storage_path = Path(self.storage_dir)
        storage_path.mkdir(parents=True, exist_ok=True)

        if not file_path:
            file_path = f"{storage_path}/{self.workflow_id}_{self.execution_id}.json"
        else:
            file_path = file_path

        data = self.model_dump(mode="json")

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

        return str(file_path)

    @classmethod
    def load_from_file(cls, file_path: Union[str, Path]) -> "WorkflowExecution":
        """Load workflow execution from a file.

        Args:
            file_path: Path to the saved execution file

        Returns:
            WorkflowExecution instance

        Raises:
            FileNotFoundError: If the file doesn't exist

        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"No execution file found at {file_path}")

        with open(file_path, "r") as f:
            data = json.load(f)

        for key in ["start_time", "end_time"]:
            if data.get(key):
                data[key] = datetime.fromisoformat(data[key])

        for _, executions in data.get("task_executions", {}).items():
            for execution in executions:
                for key in ["start_time", "end_time"]:
                    if execution.get(key):
                        execution[key] = datetime.fromisoformat(execution[key])

        return cls(**data)

    @classmethod
    def list_executions(
        cls, workflow_id: Optional[str] = None, storage_dir: str = "./workflow_executions"
    ) -> List[Dict[str, Any]]:
        """List all saved workflow executions.

        Args:
            workflow_id: Optional workflow ID to filter by
            storage_dir: Directory where executions are stored

        Returns:
            List of execution summaries

        """
        storage_path = Path(storage_dir)
        if not storage_path.exists():
            return []

        executions = []
        pattern = f"{workflow_id}_*.json" if workflow_id else "*.json"

        for file_path in storage_path.glob(pattern):
            try:
                with open(file_path, "r") as f:
                    data = json.load(f)

                summary = {
                    "workflow_id": data.get("workflow_id"),
                    "execution_id": data.get("execution_id"),
                    "status": data.get("status", "unknown"),
                    "start_time": data.get("start_time"),
                    "end_time": data.get("end_time"),
                    "file_path": str(file_path),
                    "completed_tasks": sum(
                        1
                        for execs in data.get("task_executions", {}).values()
                        for exe in execs
                        if exe.get("status") == "COMPLETED"
                    ),
                    "failed_tasks": sum(
                        1 for execs in data.get("task_executions", {}).values() for exe in execs if exe.get("status") == "FAILED"
                    ),
                }
                executions.append(summary)
            except Exception:
                continue

        executions.sort(key=lambda x: x.get("start_time", ""), reverse=True)
        return executions

    def update_task_execution(self, task_execution: TaskExecution) -> None:
        """Update a task execution and save to file."""
        for executions in self.task_executions.values():
            for i, execution in enumerate(executions):
                if execution.execution_id == task_execution.execution_id:
                    executions[i] = task_execution
                    self.save_to_file()
                    return
        raise ValueError(f"No task execution found with ID {task_execution.execution_id}")

    @property
    def duration(self) -> Optional[float]:
        """Calculate the total duration of the workflow execution in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None

    @property
    def completed_tasks(self) -> int:
        """Count of completed tasks."""
        return sum(
            1
            for executions in self.task_executions.values()
            for execution in executions
            if execution.status == TaskStatus.COMPLETED
        )

    @property
    def failed_tasks(self) -> int:
        """Count of failed tasks."""
        return sum(
            1 for executions in self.task_executions.values() for execution in executions if execution.status == TaskStatus.FAILED
        )

    @property
    def pending_tasks(self) -> int:
        """Count of pending tasks."""
        return sum(
            1
            for executions in self.task_executions.values()
            for execution in executions
            if execution.status == TaskStatus.PENDING
        )

    @property
    def running_tasks(self) -> int:
        """Count of running tasks."""
        return sum(
            1
            for executions in self.task_executions.values()
            for execution in executions
            if execution.status == TaskStatus.RUNNING
        )
