import asyncio
import inspect
import os
import uuid
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Awaitable, Callable, Dict, List, Optional, Set, Union

from pydantic import ConfigDict, Field
from pydantic.dataclasses import dataclass

from igniteai_agent_sdk._event_emitter import EventEmitter
from igniteai_agent_sdk.config import settings
from igniteai_agent_sdk.types import (
    Event,
    Task,
    TaskFunction,
    WorkflowErrorEvent,
    WorkflowStartEvent,
    WorkflowStopEvent,
)
from igniteai_agent_sdk.workflow._executors import TaskExecution, WorkflowExecution


@dataclass(config=ConfigDict(arbitrary_types_allowed=True))
class Workflow:
    """A workflow system that executes tasks based on events.

    Tasks can be triggered by events and can emit additional events to trigger
    further tasks. The workflow supports both synchronous and asynchronous tasks,
    and parallel execution of tasks triggered by the same event.
    """

    name: str
    workflow_id: str = Field(default=str(uuid.uuid4()))
    event_emitter: EventEmitter = EventEmitter()
    tasks: Dict[str, List[Task]] = Field(default_factory=dict)
    running_tasks: Set[str] = Field(default_factory=set)
    thread_pool: ThreadPoolExecutor = Field(default_factory=lambda: ThreadPoolExecutor(max_workers=None))
    max_concurrency: int = Field(default=10)
    _is_running: bool = Field(default=False)
    _is_paused: bool = Field(default=False)
    current_execution: Optional[WorkflowExecution] = None
    task_executions: Dict[str, TaskExecution] = Field(default_factory=dict)
    execution_history: List[WorkflowExecution] = Field(default_factory=list)
    storage_dir: str = Field(default_factory=lambda: str(settings.WORKFLOW_DIR))
    workflow_state: Dict[str, Any] = Field(default_factory=dict)

    def __post_init__(self):
        self.semaphore = asyncio.Semaphore(self.max_concurrency)
        os.makedirs(self.storage_dir, exist_ok=True)
        self.current_execution = WorkflowExecution(workflow_id=self.workflow_id, storage_dir=self.storage_dir)

    def task(
        self,
        event_type: type[Event],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Callable[[TaskFunction], TaskFunction]:
        """Decorator to register a function as a workflow task.

        Example:
            @workflow.task(CustomEvent)
            async def task_name(event: CustomEvent) -> NextEvent:
                return NextEvent(...)

        Args:
            event_type: The Event class that will trigger this task
            metadata: Optional metadata for the task

        Returns:
            Decorator function that registers the decorated function as a task

        Raises:
            TypeError: If the task function doesn't have a return type annotation that is a subclass of Event

        """

        def decorator(func: TaskFunction) -> TaskFunction:
            self.add_task(
                event_type=event_type,
                func=func,
                metadata=metadata,
            )
            return func

        return decorator

    def validate_workflow(self) -> Dict[str, bool]:
        """Validates if the workflow has essential event handlers.

        Checks if the workflow has handlers for WorkflowStartEvent,
        which is required for proper workflow execution.

        Raises:
            ValueError: If start event handler is missing

        """
        has_start_handler = "WorkflowStartEvent" in self.tasks and len(self.tasks["WorkflowStartEvent"]) > 0

        validation = {"has_start_handler": has_start_handler, "has_stop_handler": True}

        if not has_start_handler:
            raise ValueError(
                f"Workflow '{self.name}' is missing a required event handler: WorkflowStartEvent. "
                "Make sure to register a handler for WorkflowStartEvent."
            )

        return validation

    def add_task(
        self,
        event_type: type[Event],
        func: TaskFunction,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Add a task to be triggered when a specific event is emitted.

        Args:
            event_type: The Event class that will trigger this task
            func: Function to execute when the event is triggered
            metadata: Optional metadata for the task

        Returns:
            The task ID (generated if not provided)

        """
        signature = inspect.signature(func)
        return_annotation = signature.return_annotation

        if return_annotation == inspect.Signature.empty or return_annotation is None:
            raise TypeError(
                f"Task function {func.__name__} must return an Event subclass, "
                f"got {return_annotation} which doesn't contain any Event types"
            )

        elif isinstance(return_annotation, type) and issubclass(return_annotation, Event):
            inferred_emitted_events = [return_annotation]

        elif hasattr(return_annotation, "__origin__") and return_annotation.__origin__ is Union:
            inferred_emitted_events = []
            args = getattr(return_annotation, "__args__", [])
            for arg in args:
                if isinstance(arg, type) and issubclass(arg, Event):
                    inferred_emitted_events.append(arg)

            if not inferred_emitted_events:
                raise TypeError(
                    f"Task function {func.__name__} with Union return type must contain at least one Event subclass, "
                    f"got {return_annotation} which doesn't contain any Event types"
                )

        elif hasattr(return_annotation, "__origin__") and return_annotation.__origin__ in (list, List):
            if len(getattr(return_annotation, "__args__", [])) == 1:
                element_type = return_annotation.__args__[0]
                if isinstance(element_type, type) and issubclass(element_type, Event):
                    inferred_emitted_events = [element_type]
                else:
                    raise TypeError(
                        f"Task function {func.__name__} must return an Event subclass or List[Event], "
                        f"got {return_annotation} with non-Event element type"
                    )
            else:
                raise TypeError(
                    f"Task function {func.__name__} must return an Event subclass or List[Event], "
                    f"got {return_annotation} with complex type arguments"
                )
        else:
            raise TypeError(f"Task function {func.__name__} must return an Event subclass, got {return_annotation}")

        is_async = inspect.iscoroutinefunction(func)

        event_name = self._get_event_name(event_type)

        task = Task(
            id=func.__name__,
            func=func,
            is_async=is_async,
            event_type=event_type,
            emitted_events=inferred_emitted_events,
            metadata={} if metadata is None else metadata,
        )

        if event_name not in self.tasks:
            self.tasks[event_name] = []

        self.tasks[event_name].append(task)

        self.event_emitter.on(event_type, self._create_task_handler(task))
        return task.id

    def _get_event_name(self, event_type: type[Event]) -> str:
        """Extract the event name string from an Event class."""
        if hasattr(event_type, "__name__"):
            return event_type.__name__
        return str(event_type)

    def _create_task_handler(self, task: Task) -> Callable[[Event], Awaitable[None]]:
        """Create an event handler for a task."""

        async def handler(event: Event) -> None:
            await self._execute_task(task, event)

        return handler

    async def _execute_task(self, task: Task, event: Event) -> Any:
        """Execute a task with the given event data."""
        async with self.semaphore:
            self.running_tasks.add(task.id)
            result = None

            task_execution = None
            if self.current_execution:
                task_execution = self.current_execution.register_task_execution(task.id)
                task_execution.start(event_data=event.model_dump() if hasattr(event, "model_dump") else vars(event))
                self.task_executions[task_execution.execution_id] = task_execution

            try:
                if task.timeout:
                    if task.is_async:
                        result = await asyncio.wait_for(task.func(event), timeout=task.timeout)
                    else:
                        loop = asyncio.get_event_loop()
                        result = await asyncio.wait_for(
                            loop.run_in_executor(self.thread_pool, task.func, event), timeout=task.timeout
                        )
                else:
                    if task.is_async:
                        result = await task.func(event)
                    else:
                        loop = asyncio.get_event_loop()
                        result = await loop.run_in_executor(self.thread_pool, task.func, event)

                if isinstance(result, list) and all(isinstance(item, Event) for item in result):
                    batch_size = 10
                    for i in range(0, len(result), batch_size):
                        batch = result[i : i + batch_size]
                        await asyncio.gather(*[self.event_emitter.emit(evt) for evt in batch])
                elif isinstance(result, Event):
                    await self.event_emitter.emit(result)

                if task_execution:
                    task_execution.complete(result)
                    if self.current_execution:
                        self.current_execution.update_task_execution(task_execution)

                return result

            except asyncio.TimeoutError:
                error_msg = f"Task {task.id} timed out after {task.timeout} seconds"
                error_event = WorkflowErrorEvent(task_id=task.id, workflow_id=self.workflow_id, error=error_msg)
                await self.event_emitter.emit(error_event)

                if task_execution:
                    task_execution.fail(error_msg)
                    if self.current_execution:
                        self.current_execution.update_task_execution(task_execution)

                return None

            except Exception as e:
                error_event = WorkflowErrorEvent(task_id=task.id, workflow_id=self.workflow_id, error=str(e))
                await self.event_emitter.emit(error_event)

                if task_execution:
                    task_execution.fail(str(e))
                    if self.current_execution:
                        self.current_execution.update_task_execution(task_execution)

                return None

            finally:
                self.running_tasks.discard(task.id)

                if task_execution:
                    self.task_executions.pop(task_execution.execution_id, None)

    async def start(self, initial_data: Optional[Dict[str, Any]] = None) -> Any:
        """Start the workflow by emitting a WorkflowStartEvent.

        Args:
            initial_data: Optional initial data for the workflow

        Returns:
            The final result when the workflow completes

        Raises:
            ValueError: If the workflow is missing required event handlers
            RuntimeError: If the workflow is already running

        """
        self.validate_workflow()

        if self._is_running:
            raise RuntimeError("Workflow is already running")

        self._is_running = True
        final_result = None

        async def capture_stop_event(event: WorkflowStopEvent):
            nonlocal final_result
            final_result = event.result

        self.event_emitter.once(WorkflowStopEvent, capture_stop_event)

        self.current_execution.start(initial_data)  # type: ignore

        try:
            start_event = WorkflowStartEvent(workflow_id=self.workflow_id, **initial_data if initial_data else {})
            await self.event_emitter.emit(start_event)

            # Wait for workflow to complete - either all tasks finish or we get a stop event
            while self.running_tasks or (self._is_paused and final_result is None):
                if self._is_paused:
                    await asyncio.sleep(0.1)
                else:
                    await asyncio.sleep(0.01)

            self.current_execution.complete(final_result)  # type: ignore
            self.execution_history.append(self.current_execution)  # type: ignore

            return final_result

        except Exception as e:
            if self.current_execution:
                self.current_execution.fail(str(e))
            error_event = WorkflowErrorEvent(task_id=None, workflow_id=self.workflow_id, error=str(e))
            await self.event_emitter.emit(error_event)
            raise

        finally:
            self._is_running = False
            self._is_paused = False
            self.current_execution = None

    def remove_task(self, task_id: str) -> bool:
        """Remove a task from the workflow.

        Args:
            task_id: The ID of the task to remove

        Returns:
            True if the task was found and removed, False otherwise

        """
        for event_name, tasks in self.tasks.items():
            for i, task in enumerate(tasks):
                if task.id == task_id:
                    tasks.pop(i)

                    if not tasks:
                        self.tasks.pop(event_name)
                    return True
        return False

    async def cleanup(self) -> None:
        """Clean up resources used by the workflow."""
        self.thread_pool.shutdown(wait=False)

    def get_current_progress(self) -> Dict[str, Any]:
        """Get the current progress of the workflow execution."""
        if not self.current_execution:
            return {"status": "not_running", "workflow_id": self.workflow_id, "name": self.name}

        return {
            "status": "running" if self._is_running else "stopping",
            "workflow_id": self.workflow_id,
            "name": self.name,
            "start_time": self.current_execution.start_time.isoformat() if self.current_execution.start_time else None,
            "duration": self.current_execution.duration,
            "tasks": {
                "total": len(self.tasks),
                "completed": self.current_execution.completed_tasks,
                "failed": self.current_execution.failed_tasks,
                "pending": self.current_execution.pending_tasks,
                "running": self.current_execution.running_tasks,
            },
        }

    async def pause(self) -> None:
        """Pause the workflow execution.

        This does not stop currently executing tasks but prevents new tasks from starting.
        The workflow remains paused until resume() is called.
        """
        if self._is_running and not self._is_paused:
            self._is_paused = True
            if self.current_execution:
                self.current_execution.status = "paused"
                self.current_execution.save_to_file()

    async def resume(self) -> None:
        """Resume a paused workflow execution."""
        if self._is_running and self._is_paused:
            self._is_paused = False
            if self.current_execution:
                self.current_execution.status = "running"
                self.current_execution.save_to_file()

    def is_paused(self) -> bool:
        """Check if the workflow is currently paused.

        Returns:
            True if the workflow is paused, False otherwise

        """
        return self._is_paused
