import asyncio
import datetime
from copy import deepcopy
from typing import Any

from pydantic import BaseModel


def deep_merge_dicts(base: dict, update: dict) -> dict:
    result = deepcopy(base)
    for k, v in update.items():
        if k in result and isinstance(result[k], dict) and isinstance(v, dict):
            result[k] = deep_merge_dicts(result[k], v)
        else:
            result[k] = v
    return result


class Context(BaseModel):
    context: dict = {}
    _lock: asyncio.Lock = asyncio.Lock()
    history: list = []

    async def aupdate(self, new_context: dict, author: str = "system") -> None:
        """Merge new_context into existing context and record a history entry with timestamp."""
        async with self._lock:
            old_context_snapshot = self.context.copy()

            self.context = deep_merge_dicts(self.context, new_context)
            self.history.append(
                {
                    "timestamp": datetime.datetime.now(datetime.UTC),
                    "author": author,
                    "changes": new_context,
                    "old_state": old_context_snapshot,
                    "new_state": self.context.copy(),
                }
            )

    def update(self, new_context: dict, author: str = "system") -> None:
        """Merge new_context into existing context and record a history entry with timestamp."""
        old_context_snapshot = self.context.copy()

        self.context = deep_merge_dicts(self.context, new_context)
        self.history.append(
            {
                "timestamp": datetime.datetime.now(datetime.UTC),
                "author": author,
                "changes": new_context,
                "old_state": old_context_snapshot,
                "new_state": self.context.copy(),
            }
        )

    async def aset(self, key: str, value: Any) -> None:
        await self.aupdate({key: value})

    def set(self, key: str, value: Any) -> None:
        self.update({key: value})

    async def aget(self) -> dict:
        async with self._lock:
            return dict(self.context)

    def get(self) -> dict:
        return dict(self.context)

    async def areset(self) -> None:
        async with self._lock:
            self.context = {}

    def reset(self) -> None:
        self.context = {}

    async def arollback(self, step: int = 1):
        """Roll back context changes by the specified number of steps."""
        if step <= 0 or step > len(self.history):
            return

        async with self._lock:
            rollback_point = len(self.history) - step
            old_state = self.history[rollback_point]["old_state"]
            self.context = old_state
            self.history = self.history[:rollback_point]

    def rollback(self, step: int = 1):
        """Roll back context changes by the specified number of steps."""
        if step <= 0 or step > len(self.history):
            return

        rollback_point = len(self.history) - step
        old_state = self.history[rollback_point]["old_state"]
        self.context = old_state
        self.history = self.history[:rollback_point]
