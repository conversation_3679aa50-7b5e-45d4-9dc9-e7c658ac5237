import asyncio
import json
import re
from datetime import datetime
from pathlib import Path
from typing import Any, AsyncGenerator, Callable, Dict, List, Literal, Optional, Set, Tuple, Type, Union, cast
from uuid import uuid4

from pydantic import BaseModel, ConfigDict, Field
from pydantic.dataclasses import dataclass

from igniteai_agent_sdk._agent_context import Context
from igniteai_agent_sdk._event_emitter import EventEmitter
from igniteai_agent_sdk.config import settings
from igniteai_agent_sdk.guardrails import Guardrail
from igniteai_agent_sdk.llm import LLM
from igniteai_agent_sdk.llm.types import ChatCompletionMessage as Message
from igniteai_agent_sdk.media import Audio, Image
from igniteai_agent_sdk.memory.memory_manager import MemoryManager
from igniteai_agent_sdk.prompts import template_manager
from igniteai_agent_sdk.telemetry import Telemetry
from igniteai_agent_sdk.tools.agent_handoff import get_handoff_tools, handoff_manager
from igniteai_agent_sdk.tools.base_tool import Tool
from igniteai_agent_sdk.tools.mcp import MCPServer
from igniteai_agent_sdk.tools.memory_tool import get_memory_tools
from igniteai_agent_sdk.tools.toolkit import ToolKit
from igniteai_agent_sdk.types import (
    AgentErrorEvent,
    AgentStartEvent,
    AgentStopEvent,
    LLMResponseEvent,
    LLMStreamChunkEvent,
    LLMStreamCompleteEvent,
    LLMStreamStartEvent,
    Response,
    StreamChunk,
)
from igniteai_agent_sdk.usage_tracker import UsageTracker
from igniteai_agent_sdk.utils import debug_print, get_json_schema

ToolCallable = Callable[..., Any]
ToolType = Union[Tool, ToolCallable]
SystemPromptType = Union[str, Callable[[], Optional[str]]]
HistoryItemType = Union[Message, Dict[str, Any]]
MemoryEntry = Dict[str, Any]
ToolChoiceType = Union[Literal["auto", "none"], Dict[str, Any]]
AgentConfigDict = Dict[str, Any]


json_pattern = re.compile(r"```json(.*?)```", re.DOTALL)


@dataclass(config=ConfigDict(arbitrary_types_allowed=True))
class Agent:
    name: str
    agent_id: Optional[str] = None
    introduction: str = "you are a helpful assistant"
    role: Optional[str] = None
    task: str = "help the user with their query"
    instructions: Optional[str] = None
    execute_tools: bool = True
    agents: Optional[List["Agent"]] = None
    context: Union[Dict[str, Any], Context] = Field(default_factory=Context)
    mcp_servers: Optional[List[MCPServer]] = None
    verbose: bool = False

    model: str = Field(default_factory=lambda: settings.DEFAULT_MODEL)
    tools: Optional[Union[ToolKit, List[ToolType]]] = None
    response_format: Optional[Type[BaseModel]] = None
    llm_provider_config: Optional[Dict[str, Any]] = None
    temperature: float = settings.LLM_TEMPERATURE
    top_p: float = settings.LLM_TOP_P
    max_completion_tokens: int = settings.LLM_MAX_TOKENS

    memory_enabled: bool = False
    memory_type: str = "file"
    memory_config: Optional[Dict[str, Any]] = None

    input_guardrails: Optional[List[Guardrail]] = None
    output_guardrails: Optional[List[Guardrail]] = None

    def __post_init__(self):
        self.agent_id = self.agent_id or str(uuid4())
        self._context = Context(context=self.context) if isinstance(self.context, dict) else self.context
        self.event_emitter = EventEmitter()
        self._json_schema_cache = {}
        self.usage_tracker = UsageTracker()
        self.telemetry = Telemetry(agent_name=self.name)
        self.telemetry.subscribe_to_agent(self)

        self._init_memory()

        self.normalize_tools()
        self.init_llm()
        debug_print(self.verbose, f"Initialized agent '{self.name}' (ID: {self.agent_id}) with model {self.model}")

    def _init_memory(self):
        """Initialize memory manager based on configuration."""
        if not self.memory_enabled:
            self.memory = None
            debug_print(self.verbose, f"Agent '{self.name}' initialized without memory")
            return

        memory_config = self.memory_config or {}
        if not memory_config.get("directory") and settings.MEMORY_DIR:
            memory_config["directory"] = str(settings.MEMORY_DIR)

        self.memory = MemoryManager(memory_type=self.memory_type, verbose=self.verbose, **memory_config)
        debug_print(self.verbose, f"Agent '{self.name}' initialized with {self.memory_type} memory")

    def init_llm(self):
        self.provider_name, self.model_name = self.model.split(":")
        self._llm = LLM(self.provider_name, self.llm_provider_config)

    def normalize_tools(self) -> None:
        """Process and normalize tools for the agent.

        Converts different tool formats into a standardized ToolKit instance
        and handles integration of agent transfer, memory, and handoff tools.
        """
        self.tools_instance = ToolKit()
        additional_tools = []

        self._process_provided_tools(additional_tools)

        self._add_special_tools(additional_tools)

        if additional_tools:
            self.tools_instance._add_tools(additional_tools)

    def _process_provided_tools(self, tools_list: List[ToolType]) -> None:
        """Process and validate tools provided to the agent."""
        if not self.tools:
            return

        if isinstance(self.tools, ToolKit):
            self.tools_instance = self.tools
            return

        for tool in self.tools:
            if not callable(tool) and not isinstance(tool, Tool):
                raise ValueError("One or more tools is not callable or not a Tool instance")
        tools_list.extend(cast(List[ToolType], self.tools))

    def _add_special_tools(self, tools_list: List[ToolType]) -> None:
        """Add special tools like agent handoff and memory tools."""
        if self.has_agents:
            for _agent in cast(List["Agent"], self.agents):
                _agent._context = self._context
                handoff_manager.register_worker(_agent)
            tools_list.extend(get_handoff_tools(self))

        if self.memory:
            tools_list.extend(get_memory_tools(self))

    @classmethod
    def from_dict(cls, config: AgentConfigDict) -> "Agent":
        """Create an agent from a dictionary configuration.

        Args:
            config: A dictionary with agent configuration parameters.

        Returns:
            An initialized Agent instance.

        """
        if config.get("agents"):
            config["agents"] = [cls.from_dict(agent_config) for agent_config in config["agents"]]

        if config.get("mcp_servers"):
            config["mcp_servers"] = [MCPServer(**server_config) for server_config in config["mcp_servers"]]

        valid_params = {k: v for k, v in config.items() if k in cls.__annotations__}

        return cls(**valid_params)

    async def _apply_input_guardrails(self, message_history: List[Message]) -> None:
        """Apply input guardrails if the last message is from a user."""
        if message_history[-1].role == "user" and self.input_guardrails:
            input_content = message_history[-1].content
            guardrail_tasks = [guardrail.function(self, input_content) for guardrail in self.input_guardrails]
            await asyncio.gather(*guardrail_tasks)

    async def _apply_output_guardrails(self, structured_output: Any) -> None:
        """Apply output guardrails to the structured output."""
        if self.output_guardrails:
            guardrail_tasks = [guardrail.function(self, structured_output) for guardrail in self.output_guardrails]
            await asyncio.gather(*guardrail_tasks)

    @classmethod
    def from_json(cls, json_data: Union[str, Path, AgentConfigDict]) -> "Agent":
        """Create an agent from a JSON string or file path.

        Args:
            json_data: Either a JSON string, a path to a JSON file, or a dictionary.

        Returns:
            An initialized Agent instance.

        """
        if isinstance(json_data, (str, Path)):
            path = Path(json_data)
            if path.exists() and path.is_file():
                with open(path, "r") as f:
                    config: AgentConfigDict = json.load(f)
            else:
                config = json.loads(json_data) if isinstance(json_data, str) else {}
        elif isinstance(json_data, dict):
            config = json_data
        else:
            raise ValueError("json_data must be a JSON string, file path, or dictionary")

        return cls.from_dict(config)

    def save_to_json(self, file_path: Union[str, Path]) -> None:
        """Save the agent configuration to a JSON file.

        Args:
            file_path: Path where to save the JSON file.

        """
        file_path = Path(file_path)

        file_path.parent.mkdir(parents=True, exist_ok=True)

        config = self.to_dict()
        with open(file_path, "w") as f:
            json.dump(config, f, indent=2)

    @property
    def has_agents(self) -> bool:
        return bool(self.agents and len(self.agents) > 0)

    @property
    def llm(self) -> LLM:
        return self._llm

    async def recall_by_run(self, run_id: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Retrieve memories by run ID.

        Args:
            run_id: The run ID to retrieve memories for (defaults to current run ID if set)
            limit: Maximum number of memories to retrieve

        Returns:
            List of memory entries for the specified run

        """
        if not self.memory:
            debug_print(self.verbose, f"Agent '{self.name}' attempted to recall by run but memory is disabled")
            return []

        results = await self.memory.recall_by_run(run_id, limit)

        return results

    async def get_memory_run_ids(self) -> Set[str]:
        """Get all unique run IDs in the agent's memory.

        Returns:
            Set of unique run IDs or empty set if memory is disabled

        """
        if not self.memory:
            debug_print(self.verbose, f"Agent '{self.name}' attempted to get memory run IDs but memory is disabled")
            return set()

        run_ids = await self.memory.get_run_ids()
        return run_ids

    async def remember(
        self, content: Any, key: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None, run_id: Optional[str] = None
    ) -> Optional[str]:
        """Store information in agent's memory.

        Args:
            content: Content to remember
            key: Optional key for the memory entry
            metadata: Optional metadata for the memory entry
            run_id: Optional run ID (defaults to current run ID if set)

        Returns:
            The key of the stored memory or None if memory is disabled

        """
        if not self.memory:
            debug_print(self.verbose, f"Agent '{self.name}' attempted to remember but memory is disabled")
            return None

        memory_key = await self.memory.remember(content, key, metadata, run_id)
        return memory_key

    async def recall_by_query(self, query: str, limit: int = 5, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search agent's memory by query.

        Args:
            query: The search query
            limit: Maximum number of results
            run_id: Optional run ID (defaults to current run ID if set)

        Returns:
            List of matching memory entries or empty list if memory is disabled

        """
        if not self.memory:
            debug_print(self.verbose, f"Agent '{self.name}' attempted to recall by query but memory is disabled")
            return []

        results = await self.memory.recall_by_query(query, limit)
        return results

    async def recall_recent(self, limit: int = 5, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Retrieve recent memories.

        Args:
            limit: Maximum number of memories to retrieve
            run_id: Optional run ID (defaults to current run ID if set)

        Returns:
            List of recent memory entries or empty list if memory is disabled

        """
        if not self.memory:
            debug_print(self.verbose, f"Agent '{self.name}' attempted to recall recent memories but memory is disabled")
            return []

        results = await self.memory.recall_recent(limit, run_id)

        return results

    async def initialize_mcp_servers(self):
        if not self.mcp_servers:
            return

        init_tasks = [server.initialize() for server in self.mcp_servers]
        try:
            await asyncio.gather(*init_tasks)

            for server in self.mcp_servers:
                if server._initialized:
                    tools = await server.list_tools()
                    self.tools_instance._add_tools(tools)

            self._json_schema_cache.clear()

        except Exception as e:
            await self.event_emitter.emit(AgentErrorEvent(error=str(e)))
            raise

    def to_dict(self) -> AgentConfigDict:
        """Convert agent configuration to a dictionary.

        Returns:
            Dictionary representation of the agent configuration

        """
        result: AgentConfigDict = {
            "name": self.name,
            "agent_id": self.agent_id,
            "introduction": self.introduction,
            "role": self.role,
            "task": self.task,
            "instructions": self.instructions,
            "execute_tools": self.execute_tools,
            "agents": [agent.to_dict() for agent in self.agents] if self.agents else None,
            "model": self.model,
            "verbose": self.verbose,
            "memory_enabled": self.memory_enabled,
            "memory_type": self.memory_type,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "max_completion_tokens": self.max_completion_tokens,
            "context": self._context.get(),
        }

        if self.memory_config:
            result["memory_config"] = self.memory_config

        if self.llm_provider_config:
            result["llm_provider_config"] = self.llm_provider_config

        return result

    def get_transfer_prompt(self) -> str:
        """Generate a prompt for transferring tasks to other agents.
        This is cached to avoid regenerating the same prompt multiple times.
        """
        transfer_prompt = [
            "<agent_team>\n",
            "You are the leader of a team of AI Agents:\n",
            "- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them.\n",
            "- If you transfer a task to another Agent, make sure to include:\n",
            "  - task_description (str): A clear description of the task.\n",
            "  - expected_output (str): The expected output.\n",
            "- You must always validate the output of the other Agents before responding to the user.\n",
            "- You can re-assign the task if you are not satisfied with the result.\n",
            "</agent_team>\n\n",
        ]

        return "".join(transfer_prompt)

    def get_transfer_instructions(self) -> str:
        if self.agents and self.has_agents:
            transfer_instructions = "You can transfer tasks to the following Agents in your team:\n"
            for agent_index, agent in enumerate(self.agents):
                transfer_instructions += f"\nAgent {agent_index + 1}:\n"
                if agent.name:
                    transfer_instructions += f"Name: {agent.name}\n"
                if agent.role:
                    transfer_instructions += f"Role: {agent.role}\n"
                if agent.tools_instance is not None:
                    if _tools := agent.tools_instance.get_tools().keys():
                        transfer_instructions += f"Available tools: {', '.join(_tools)}\n"
            return transfer_instructions
        return ""

    async def prepare_user_msg(
        self, user_msg: str, images: Optional[List[Image]] = None, audios: Optional[List[Audio]] = None
    ) -> Message:
        if images is not None and images:
            tasks = [image.load() for image in images]
            await asyncio.gather(*tasks)

        if audios is not None and audios:
            tasks = [audio.load() for audio in audios]
            await asyncio.gather(*tasks)

        return Message(role="user", content=user_msg, image_inputs=images, audio_inputs=audios)

    def build_system_prompt(self) -> Message:
        """Build a system prompt for the agent using the template manager or custom prompt.

        Returns:
            A Message object with the system prompt content

        """
        debug_print(self.verbose, f"Agent '{self.name}' building system prompt with template manager")

        transfer_prompt = self._get_transfer_prompt_if_needed()

        instructions_text = self._build_instructions_text()

        return self._format_final_prompt(transfer_prompt, instructions_text)

    def _get_transfer_prompt_if_needed(self) -> str:
        """Get transfer prompt if agent has sub-agents."""
        if self.has_agents and self.agents:
            return self.get_transfer_prompt() or ""
        return ""

    def _build_instructions_text(self) -> str:
        """Build the instructions section of the prompt."""
        instructions_text = ""

        if self.instructions is not None:
            instructions_text += f"<Instructions>:\n{self.instructions}\n"

            if self.has_agents and self.agents:
                instructions_text += f"<transfer_instructions>{self.get_transfer_instructions()}</transfer_instructions>"

            instructions_text += "</Instructions>"

        if self.response_format:
            instructions_text += get_json_schema(self.response_format)

        return instructions_text

    def _format_final_prompt(self, transfer_prompt: str, instructions_text: str) -> Message:
        """Format and return the final system prompt."""
        try:
            if self._context.get():
                additional_context = f"Additional context you can refer to:\n{self._context.get()}"
            else:
                additional_context = ""

            final_prompt = template_manager.format_template(
                "base",
                introduction=self.introduction,
                role=self.role or "Assistant",
                task=self.task,
                transfer_prompt=transfer_prompt,
                instructions=instructions_text,
                additional_context=additional_context,
            )
            debug_print(self.verbose, f"Agent '{self.name}' system prompt built ({len(final_prompt)} chars)")
            return Message(role="system", content=final_prompt)
        except Exception as e:
            debug_print(self.verbose, f"Agent '{self.name}' error using template_manager: {str(e)}. Falling back to default.")
            return Message(role="system", content="You are a helpful assistant.")

    def build_model_run_params(self) -> Tuple[Dict[str, Any], Optional[List]]:
        run_config = {}
        tools = None
        if self.tools_instance.get_tools():
            tools = self.tools_instance.get_json_schema()
        run_config["temperature"] = self.temperature
        run_config["top_p"] = self.top_p
        run_config["max_completion_tokens"] = self.max_completion_tokens

        return run_config, tools

    async def _process_structured_output(self, response_content: str) -> str:
        """Process structured output from the response if needed."""
        if not self.response_format or not response_content:
            return response_content

        try:
            match = json_pattern.search(response_content)
            if match:
                response_content = match.group(1).strip()
            output_data = json.loads(response_content)
            structured_output = self.response_format.model_validate(output_data)
            await self._apply_output_guardrails(structured_output)
            return json.dumps(output_data)
        except Exception as e:
            debug_print(self.verbose, f"Error processing structured output: {e}")
            raise e

    async def run(
        self,
        user_msg: str,
        images: Optional[List[Image]] = None,
        audios: Optional[List[Audio]] = None,
        history: Optional[list[Union[Message, dict]]] = None,
        max_turns: float = float("inf"),
        run_id: Optional[str] = None,
    ) -> Response:
        """Run the agent with the given user message and history.

        Args:
            user_msg: The user's input message
            images: Optional list of images to be processed
            audios: Optional list of audio files to be processed
            history: Conversation history
            max_turns: Maximum number of turns to take in this dialogue
            run_id: Optional ID for the run

        Returns:
            response

        """
        try:
            run_id = await self._initialize_run(run_id, user_msg)

            history_msgs = await self._prepare_conversation_history(user_msg, history, images, audios)

            llm_message, turn = await self._run_conversation_loop(history_msgs, max_turns)

            response = await self._create_response(llm_message, history_msgs, user_msg, turn)

            await self._finalize_run(response, llm_message, user_msg, turn)

            return response

        except Exception as e:
            await self._handle_run_error(e)
            raise

        finally:
            await self._cleanup_mcp_servers()

    async def run_stream(
        self,
        user_msg: str,
        images: Optional[List[Image]] = None,
        audios: Optional[List[Audio]] = None,
        history: Optional[list[Union[Message, dict]]] = None,
        max_turns: float = float("inf"),
        run_id: Optional[str] = None,
    ) -> AsyncGenerator[StreamChunk, None]:
        """Run the agent with streaming response support.

        Args:
            user_msg: The user's input message
            images: Optional list of images to be processed
            audios: Optional list of audio files to be processed
            history: Conversation history
            max_turns: Maximum number of turns to take in this dialogue
            run_id: Optional ID for the run

        Yields:
            StreamChunk objects containing incremental response data

        """
        try:
            run_id = await self._initialize_run(run_id, user_msg)

            history_msgs = await self._prepare_conversation_history(user_msg, history, images, audios)

            accumulated_content = ""
            total_chunks = 0

            async for chunk in self._run_conversation_loop_stream(history_msgs, max_turns):
                if chunk.delta:
                    accumulated_content += chunk.delta

                chunk.agent = self.name
                chunk.run_id = self.run_id

                await self.event_emitter.emit(
                    LLMStreamChunkEvent(
                        model=self.model,
                        chunk=chunk,
                        agent_name=self.name,
                        run_id=self.run_id,
                    )
                )

                total_chunks += 1

                if chunk.is_final:
                    final_response = await self._create_streaming_response(
                        accumulated_content, history_msgs, user_msg, chunk.tool_calls, total_chunks
                    )
                    chunk.final_response = final_response
                    await self.event_emitter.emit(
                        LLMStreamCompleteEvent(
                            model=self.model,
                            full_content=accumulated_content,
                            tool_calls=chunk.tool_calls,
                            agent_name=self.name,
                            run_id=self.run_id,
                            total_chunks=total_chunks,
                        )
                    )

                    await self._finalize_streaming_run(final_response, accumulated_content, user_msg)
                    yield chunk
                    return
                yield chunk

        except Exception as e:
            await self._handle_run_error(e)
            raise

        finally:
            await self._cleanup_mcp_servers()

    async def _run_conversation_loop_stream(self, history: list[Message], max_turns: float) -> AsyncGenerator[StreamChunk, None]:
        """Run the streaming conversation loop between the agent and tools."""
        model_run_params, tools = self.build_model_run_params()
        turn = 0

        while turn < max_turns:
            turn += 1
            debug_print(self.verbose, f"Agent '{self.name}' starting streaming turn {turn}/{max_turns}")

            await self.event_emitter.emit(
                LLMStreamStartEvent(
                    model=self.model,
                    agent_name=self.name,
                    run_id=self.run_id,
                )
            )

            accumulated_message = None
            async for chunk in self._get_llm_response_stream(history, tools or [], model_run_params):
                accumulated_message = chunk

                if chunk.is_final and chunk.tool_calls and self.execute_tools:
                    llm_message = Message(
                        content=chunk.content, role="assistant", tool_calls=self._convert_tool_calls_format(chunk.tool_calls)
                    )
                    history.append(llm_message)

                    await self._execute_tool_calls(llm_message, history)

                    break
                yield chunk
            if not accumulated_message or not accumulated_message.tool_calls or not self.execute_tools:
                debug_print(self.verbose, f"Agent '{self.name}' completed streaming without tool calls")
                break

        return

    async def _get_llm_response_stream(
        self, history: list[Message], tools: list, model_run_params: dict
    ) -> AsyncGenerator[StreamChunk, None]:
        """Get streaming response from the LLM."""
        try:
            debug_print(self.verbose, f"Agent '{self.name}' calling LLM for streaming with {len(history)} messages")

            async for chunk in self.llm.astream(
                model=self.model_name, messages=history, tools=tools, usage_tracker=self.usage_tracker, **model_run_params
            ):
                yield chunk

        except Exception as e:
            debug_print(self.verbose, f"Agent '{self.name}' encountered error during streaming LLM completion: {str(e)}")
            await self.event_emitter.emit(AgentErrorEvent(error=str(e)))
            raise

    def _convert_tool_calls_format(self, tool_calls: list) -> list:
        """Convert tool calls from streaming format to standard format."""
        if not tool_calls:
            return []

        converted = []
        for tool_call in tool_calls:
            if isinstance(tool_call, dict):
                from igniteai_agent_sdk.llm.types import ChatCompletionMessageToolCall, Function

                tc = ChatCompletionMessageToolCall()
                tc.id = tool_call.get("id", "")
                tc.type = tool_call.get("type", "function")
                tc.function = Function()
                tc.function.name = tool_call.get("function", {}).get("name", "")
                tc.function.arguments = tool_call.get("function", {}).get("arguments", "")
                converted.append(tc)
            else:
                converted.append(tool_call)
        return converted

    async def _create_streaming_response(
        self, content: str, history: list[Message], user_msg: str, tool_calls: Any, total_chunks: int
    ) -> Response:
        """Create the response object from streaming conversation results."""
        usage = self.usage_tracker.finish()

        processed_content = content
        if processed_content:
            processed_content = await self._process_structured_output(processed_content)

        response = Response(
            final_response=processed_content,
            agent=self.name,
            run_id=self.run_id,
            usage=usage,
        )

        debug_print(
            self.verbose,
            f"Agent '{self.name}' completed streaming run with response length: {len(content)} chars, {total_chunks} chunks",
        )

        return response

    async def _finalize_streaming_run(self, response: Response, content: str, user_msg: str) -> None:
        """Finalize the streaming run and emit completion events."""
        if self.memory:
            await self.remember(user_msg, content)

        await self.event_emitter.emit(
            AgentStopEvent(
                agent_name=self.name,
                run_id=self.run_id,
                response=response,
            )
        )

        debug_print(self.verbose, f"Agent '{self.name}' streaming run finalized")

    async def _initialize_run(self, run_id: Optional[str], user_msg: str) -> str:
        """Initialize the agent run and set up necessary components."""
        run_id = run_id or f"run_{datetime.now().strftime('%Y%m%d%H%M%S')}_{str(uuid4())[:8]}"
        self.run_id = run_id

        self.usage_tracker.reset()

        if self.memory:
            self.memory.set_run_id(self.run_id)

        debug_print(self.verbose, f"Agent '{self.name}' starting run with ID: {self.run_id}")

        await self.event_emitter.emit(AgentStartEvent(agent_name=self.name, run_id=self.run_id))

        if self.mcp_servers and len(self.mcp_servers) > 0:
            await self.initialize_mcp_servers()

        return run_id

    async def _prepare_conversation_history(
        self,
        user_msg: str,
        history: Optional[list[Union[Message, dict]]],
        images: Optional[List[Image]] = None,
        audios: Optional[List[Audio]] = None,
    ) -> list[Message]:
        """Prepare the conversation history for the agent run."""
        system_prompt = self.build_system_prompt()
        user_prompt = await self.prepare_user_msg(user_msg, images, audios)
        debug_print(
            self.verbose, f"Agent '{self.name}' prepared user message with context: {len(user_prompt.content or '')} chars"
        )

        msg_history = [] if history is None else history
        msg_history = [
            Message(content=hist["content"], role=hist["role"]) if isinstance(hist, dict) else hist for hist in msg_history
        ]

        msg_history.insert(0, system_prompt)
        msg_history.append(user_prompt)

        await self._apply_input_guardrails(cast(list[Message], msg_history))
        debug_print(self.verbose, f"Agent '{self.name}' prepared conversation history with {len(msg_history)} messages")

        return cast(list[Message], msg_history)

    async def _run_conversation_loop(self, history: list[Message], max_turns: float) -> Tuple[Message, int]:
        """Run the conversation loop between the agent and tools."""
        model_run_params, tools = self.build_model_run_params()
        llm_message = Message()
        turn = 0

        while turn < max_turns:
            turn += 1
            debug_print(self.verbose, f"Agent '{self.name}' starting turn {turn}/{max_turns}")

            llm_message = await self._get_llm_response(history, tools or [], model_run_params)
            history.append(llm_message)

            if not llm_message.tool_calls or not self.execute_tools:
                debug_print(self.verbose, f"Agent '{self.name}' received response without tool calls")
                break

            await self._execute_tool_calls(llm_message, history)

        return llm_message, turn

    async def _get_llm_response(self, history: list[Message], tools: list, model_run_params: dict) -> Message:
        """Get response from the LLM."""
        try:
            debug_print(self.verbose, f"Agent '{self.name}' calling LLM with {len(history)} messages")
            llm_response = await self.llm.acomplete(
                model=self.model_name, messages=history, tools=tools, usage_tracker=self.usage_tracker, **model_run_params
            )
            debug_print(self.verbose, f"Agent '{self.name}' received LLM response")

            llm_message = llm_response.choices[0].message

            await self.event_emitter.emit(
                LLMResponseEvent(model=self.model, content=llm_message.content, tool_calls=llm_message.tool_calls)
            )

            return llm_message

        except Exception as e:
            debug_print(self.verbose, f"Agent '{self.name}' encountered error during LLM completion: {str(e)}")
            await self.event_emitter.emit(AgentErrorEvent(error=str(e)))
            raise

    async def _execute_tool_calls(self, llm_message: Message, history: list[Message]) -> None:
        """Execute tool calls from LLM message and add responses to history."""
        if not llm_message.tool_calls:
            return

        tool_names = [tc.function.name for tc in llm_message.tool_calls if tc.function and tc.function.name]
        debug_print(self.verbose, f"Agent '{self.name}' received tool calls: {', '.join(tool_names)}")

        try:
            debug_print(self.verbose, f"Agent '{self.name}' executing tools")
            tools_response = await self.tools_instance.execute_tools(llm_message.tool_calls or [], self, context=self._context)
            debug_print(
                self.verbose,
                f"Agent '{self.name}' received tool execution responses: {len(tools_response.messages)} messages",
            )

            history.extend(tools_response.messages)

        except Exception as e:
            debug_print(self.verbose, f"Agent '{self.name}' error executing tools: {str(e)}")
            await self.event_emitter.emit(AgentErrorEvent(error=str(e)))
            raise

    async def _create_response(self, llm_message: Message, history: list[Message], user_msg: str, turn: int) -> Response:
        """Create the response object from the conversation results."""
        usage = self.usage_tracker.finish()

        content = llm_message.content

        if llm_message.audio is not None:
            content = llm_message.audio.transcript

        if content:
            content = await self._process_structured_output(content)

        response = Response(
            final_response=content,
            agent=self.name,
            run_id=self.run_id,
            usage=usage,
        )

        debug_print(
            self.verbose,
            f"Agent '{self.name}' completed run with {turn} turns and response length:{len(llm_message.content or '')} chars",
        )

        return response

    async def _finalize_run(self, response: Response, llm_message: Message, user_msg: str, turn: int) -> None:
        """Store response in memory and emit completion event."""
        if self.memory and llm_message.content:
            metadata = {
                "user_query": user_msg,
                "turns": turn,
                "tool_usage": bool(llm_message.tool_calls),
                "run_id": self.run_id,
            }
            await self.remember(
                content=llm_message.content, key=f"response_{datetime.now().strftime('%Y%m%d%H%M%S')}", metadata=metadata
            )
            debug_print(self.verbose, f"Agent '{self.name}' stored response in memory for run: {self.run_id}")

        await self.event_emitter.emit(
            AgentStopEvent(
                response=response,
                agent_name=self.name,
                run_id=self.run_id,
            )
        )

    async def _handle_run_error(self, error: Exception) -> None:
        """Handle errors that occur during the run."""
        debug_print(self.verbose, f"Agent '{self.name}' encountered error during run: {str(error)}")

        try:
            usage = self.usage_tracker.finish()

            error_response = Response(
                final_response=f"Error: {str(error)}",
                agent=self.name,
                run_id=self.run_id,
                usage=usage,
            )

            await self.event_emitter.emit(
                AgentErrorEvent(error=str(error), agent_name=self.name, run_id=self.run_id, response=error_response)
            )
        except Exception as e:
            await self.event_emitter.emit(AgentErrorEvent(error=str(e)))

    async def _cleanup_mcp_servers(self) -> None:
        """Clean up MCP servers if they exist."""
        if not self.mcp_servers or len(self.mcp_servers) == 0:
            return

        debug_print(self.verbose, f"Agent '{self.name}' cleaning up MCP servers")
        try:
            for server in self.mcp_servers:
                await server.cleanup()
            debug_print(self.verbose, f"Agent '{self.name}' successfully cleaned up MCP servers")
        except Exception as e:
            debug_print(self.verbose, f"Agent '{self.name}' error during MCP server cleanup: {str(e)}")
            await self.event_emitter.emit(AgentErrorEvent(error=str(e)))
