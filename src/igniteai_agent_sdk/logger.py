import logging
import sys
from typing import Optional, Union

from .config import settings

COLORS = {
    "RESET": "\033[0m",
    "BLACK": "\033[30m",
    "RED": "\033[31m",
    "GREEN": "\033[32m",
    "YELLOW": "\033[33m",
    "BLUE": "\033[34m",
    "MAGENTA": "\033[35m",
    "CYAN": "\033[36m",
    "WHITE": "\033[37m",
    "BOLD": "\033[1m",
    "UNDERLINE": "\033[4m",
    "BRIGHT_BLACK": "\033[90m",
    "BRIGHT_RED": "\033[91m",
    "BRIGHT_GREEN": "\033[92m",
    "BRIGHT_YELLOW": "\033[93m",
    "BRIGHT_BLUE": "\033[94m",
    "BRIGHT_MAGENTA": "\033[95m",
    "BRIGHT_CYAN": "\033[96m",
    "BRIGHT_WHITE": "\033[97m",
}


LEVEL_COLORS = {
    logging.DEBUG: COLORS["BRIGHT_BLUE"],
    logging.INFO: COLORS["BRIGHT_GREEN"],
    logging.WARNING: COLORS["BRIGHT_YELLOW"],
    logging.ERROR: COLORS["BRIGHT_RED"],
    logging.CRITICAL: COLORS["RED"] + COLORS["BOLD"],
}


MODULE_COLORS = {
    "igniteai_agent_sdk": COLORS["BRIGHT_MAGENTA"],
    "igniteai_agent_sdk.agent": COLORS["BRIGHT_CYAN"],
    "igniteai_agent_sdk.tools": COLORS["BRIGHT_GREEN"],
    "igniteai_agent_sdk.workflow": COLORS["BRIGHT_YELLOW"],
    "igniteai_agent_sdk.memory": COLORS["BRIGHT_BLUE"],
}


class ColorFormatter(logging.Formatter):
    """Custom log formatter with colored output for terminal."""

    def __init__(self, fmt: str, datefmt: Optional[str] = None) -> None:
        """Initialize the color formatter.

        Args:
            fmt: The log format string
            datefmt: The date format string

        """
        super().__init__(fmt, datefmt)

    def format(self, record: logging.LogRecord) -> str:
        """Format the log record with colors.

        Args:
            record: The log record to format

        Returns:
            Formatted log record string with colors

        """
        original_levelname = record.levelname
        original_name = record.name

        level_color = LEVEL_COLORS.get(record.levelno, COLORS["RESET"])
        record.levelname = f"{level_color}{original_levelname}{COLORS['RESET']}"

        module_color = COLORS["RESET"]
        for module_prefix, color in MODULE_COLORS.items():
            if record.name.startswith(module_prefix):
                module_color = color
                break
        record.name = f"{module_color}{original_name}{COLORS['RESET']}"

        result = super().format(record)

        record.levelname = original_levelname
        record.name = original_name

        return result


class NonColorFormatter(logging.Formatter):
    """Log formatter without color codes for file output."""

    def __init__(self, fmt: str, datefmt: Optional[str] = None) -> None:
        """Initialize the non-color formatter.

        Args:
            fmt: The log format string
            datefmt: The date format string

        """
        super().__init__(fmt, datefmt)


def get_logger(name: str, level: Optional[Union[str, int]] = settings.LOG_LEVEL) -> logging.Logger:
    """Get a logger configured with the appropriate level and formatting.

    Args:
        name: Name of the logger, usually __name__
        level: Optional override for log level

    Returns:
        Configured logger instance

    """
    logger = logging.getLogger(name)

    if not logger.handlers and not getattr(logger, "_configured", False):
        if level is None:
            level = settings.LOG_LEVEL

        if isinstance(level, str):
            level = getattr(logging, level.upper(), logging.INFO)

        logger.setLevel(level)  # type: ignore
        logger._configured = True  # type: ignore

        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)  # type: ignore

        console_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        console_formatter = ColorFormatter(console_format, datefmt="%Y-%m-%d %H:%M:%S")
        console_handler.setFormatter(console_formatter)

        logger.addHandler(console_handler)

    return logger


root_logger = get_logger("igniteai_agent_sdk")


def get_module_logger(name: str) -> logging.Logger:
    """Get a logger for a module within the igniteai_agent_sdk package.

    Args:
        name: The module name, usually __name__

    Returns:
        Configured logger for the module

    """
    if name.startswith("__"):
        parts = name.split(".")
        if len(parts) >= 2 and parts[-2] == "igniteai_agent_sdk":
            return get_logger(name)

    if not name.startswith("igniteai_agent_sdk"):
        name = f"igniteai_agent_sdk.{name}"

    return get_logger(name)
