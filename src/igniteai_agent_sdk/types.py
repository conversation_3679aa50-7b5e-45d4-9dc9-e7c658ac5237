import uuid
from _collections_abc import dict_items, dict_keys, dict_values
from datetime import datetime
from typing import Any, AsyncGenerator, Awaitable, Callable, Dict, List, Literal, Optional, TypeVar, Union

from pydantic import BaseModel, ConfigDict, Field, PrivateAttr
from pydantic.dataclasses import dataclass

from igniteai_agent_sdk.usage_tracker import Usage

TaskFunction = Callable[..., Any]
AsyncFunction = Callable[..., Awaitable[Any]]
T = TypeVar("T")


@dataclass
class Task:
    """Represents a task in the workflow."""

    id: str
    func: TaskFunction
    is_async: bool
    event_type: type["Event"]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    emitted_events: List[type["Event"]] = Field(default_factory=list)
    timeout: Optional[float] = None


class StreamChunk(BaseModel):
    """Represents a chunk of streamed response."""

    content: Optional[str] = None
    delta: Optional[str] = None
    finish_reason: Optional[str] = None
    tool_calls: Any = None
    agent: Optional[str] = None
    run_id: Optional[str] = None
    chunk_index: int = 0
    is_final: bool = False
    final_response: Optional["Response"] = None


class StreamResponse(BaseModel):
    """Container for streaming response data."""

    chunks: AsyncGenerator[StreamChunk, None]
    final_response: Optional["Response"] = None
    agent: Optional[str] = None
    run_id: Optional[str] = None


class Response(BaseModel):
    final_response: Any = ""
    agent: Optional[str] = None
    run_id: str
    usage: Usage

    def model_dump(
        self,
        *,
        mode: str = "python",
        include: Any = None,
        exclude: Any = None,
        context: Any = None,
        by_alias: Optional[bool] = None,
        exclude_unset: bool = False,
        exclude_defaults: bool = False,
        exclude_none: bool = False,
        round_trip: bool = False,
        warnings: Union[bool, Literal["none", "warn", "error"]] = True,
        serialize_as_any: bool = False,
        fallback: Any = None,
    ) -> Dict[str, Any]:
        """Safe model_dump that handles serialization properly."""
        try:
            return super().model_dump(
                mode=mode,
                include=include,
                exclude=exclude,
                context=context,
                by_alias=by_alias,
                exclude_unset=exclude_unset,
                exclude_defaults=exclude_defaults,
                exclude_none=exclude_none,
                round_trip=round_trip,
                warnings=warnings,
                serialize_as_any=serialize_as_any,
                fallback=fallback,
            )
        except Exception:
            # Fallback to manual serialization if Pydantic serialization fails
            return {
                "final_response": str(self.final_response) if self.final_response is not None else "",
                "agent": self.agent,
                "run_id": self.run_id,
                "usage": self.usage.model_dump() if hasattr(self.usage, "model_dump") else self.usage.to_dict(),
            }


class Result(BaseModel):
    """Encapsulates the possible return values for a tool."""

    value: str = ""
    agent: Any = None


class ToolResponse(BaseModel):
    results: List[Result] = []
    agent_name: Optional[str] = None
    messages: List = []


class Event(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    _data: Dict[str, Any] = PrivateAttr(default_factory=dict)

    def __init__(self, **params: Any):
        fields = {}
        private_attrs = {}
        data = {}
        for k, v in params.items():
            if k in self.__class__.model_fields:
                fields[k] = v
            elif k in self.__private_attributes__:
                private_attrs[k] = v
            else:
                data[k] = v

        super().__init__(**fields)
        for private_attr, value in private_attrs.items():
            super().__setattr__(private_attr, value)
        if data:
            self._data.update(data)

    def __getattr__(self, name: str) -> Any:
        if name in self.__private_attributes__ or name in self.__class__.model_fields:
            return super().__getattr__(name)  # type: ignore
        else:
            try:
                return self._data[name]
            except KeyError:
                raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'") from None

    def __setattr__(self, name: str, value: Any) -> None:
        if name in self.__private_attributes__ or name in self.__class__.model_fields:
            super().__setattr__(name, value)
        else:
            self._data[name] = value

    def __getitem__(self, key: str) -> Any:
        return self._data[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self._data[key] = value

    def get(self, key: str, default: Any = None) -> Any:
        return self._data.get(key, default)

    def __contains__(self, key: str) -> bool:
        return key in self._data

    def keys(self) -> "dict_keys[str, Any]":
        return self._data.keys()

    def values(self) -> "dict_values[str, Any]":
        return self._data.values()

    def items(self) -> "dict_items[str, Any]":
        return self._data.items()

    def __len__(self) -> int:
        return len(self._data)

    def __iter__(self) -> Any:
        return iter(self._data)

    def dict(self, *args: Any, **kwargs: Any) -> Dict[str, Any]:
        return self._data

    def __bool__(self) -> bool:
        return True

    def model_dump(
        self,
        *,
        mode: str = "python",
        include: Any = None,
        exclude: Any = None,
        context: Any = None,
        by_alias: Optional[bool] = None,
        exclude_unset: bool = False,
        exclude_defaults: bool = False,
        exclude_none: bool = False,
        round_trip: bool = False,
        warnings: Union[bool, Literal["none", "warn", "error"]] = True,
        serialize_as_any: bool = False,
        fallback: Any = None,
    ) -> Dict[str, Any]:
        """Custom model_dump that includes _data."""
        data = super().model_dump(
            mode=mode,
            include=include,
            exclude=exclude,
            context=context,
            by_alias=by_alias,
            exclude_unset=exclude_unset,
            exclude_defaults=exclude_defaults,
            exclude_none=exclude_none,
            round_trip=round_trip,
            warnings=warnings,
            serialize_as_any=serialize_as_any,
            fallback=fallback,
        )
        # Merge _data into the serialized output
        data.update(self._data)
        return data

    def model_dump_json(
        self,
        *,
        indent: Any = None,
        include: Any = None,
        exclude: Any = None,
        context: Any = None,
        by_alias: Optional[bool] = None,
        exclude_unset: bool = False,
        exclude_defaults: bool = False,
        exclude_none: bool = False,
        round_trip: bool = False,
        warnings: Union[bool, Literal["none", "warn", "error"]] = True,
        serialize_as_any: bool = False,
        fallback: Any = None,
    ) -> str:
        """Custom model_dump_json that includes _data."""
        import json

        data = self.model_dump(
            include=include,
            exclude=exclude,
            context=context,
            by_alias=by_alias,
            exclude_unset=exclude_unset,
            exclude_defaults=exclude_defaults,
            exclude_none=exclude_none,
            round_trip=round_trip,
            warnings=warnings,
            serialize_as_any=serialize_as_any,
            fallback=fallback,
        )
        return json.dumps(data, indent=indent, default=str)


class WorkflowStartEvent(Event):
    """Event emitted when a workflow starts."""

    workflow_id: str


class WorkflowStopEvent(Event):
    """Event emitted when a workflow completes."""

    result: Any


class WorkflowErrorEvent(Event):
    """Event emitted when a workflow encounters an error."""

    task_id: Optional[str] = None
    workflow_id: str
    error: str


class AgentStartEvent(Event):
    agent_name: Optional[str] = None
    run_id: Optional[str] = None


class AgentStopEvent(Event):
    agent_name: Optional[str] = None
    run_id: Optional[str] = None
    response: Optional[Any] = None


class AgentErrorEvent(Event):
    """Event emitted when an agent encounters an error."""

    error: str
    agent_name: Optional[str] = None
    run_id: Optional[str] = None
    response: Optional[Any] = None


class ToolCallEvent(Event):
    """Event emitted when a tool is called."""

    tool_name: str
    arguments: str
    agent_name: Optional[str] = None
    run_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_ms: Optional[float] = None


class LLMResponseEvent(Event):
    model: str
    content: Optional[str] = None
    tool_calls: Any = None
    agent_name: Optional[str] = None
    run_id: Optional[str] = None


class LLMStreamChunkEvent(Event):
    """Event emitted for each chunk in a streaming LLM response."""

    model: str
    chunk: StreamChunk
    agent_name: Optional[str] = None
    run_id: Optional[str] = None


class LLMStreamStartEvent(Event):
    """Event emitted when streaming starts."""

    model: str
    agent_name: Optional[str] = None
    run_id: Optional[str] = None


class LLMStreamCompleteEvent(Event):
    """Event emitted when streaming completes."""

    model: str
    full_content: Optional[str] = None
    tool_calls: Any = None
    agent_name: Optional[str] = None
    run_id: Optional[str] = None
    total_chunks: int = 0


class HumanApprovalRequestEvent(Event):
    """Event emitted when human approval is required in a workflow."""

    workflow_id: str
    task_id: str
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    prompt: str
    data: Optional[Dict[str, Any]] = None
    options: Optional[List[str]] = None
    status: str = "pending"


class HumanApprovalResponseEvent(Event):
    """Event emitted when a human provides approval or input for a workflow."""

    workflow_id: str
    task_id: str
    request_id: str
    approved: bool = False
    feedback: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
