import asyncio
import os
import shutil
from contextlib import Async<PERSON>xitStack
from typing import List, Optional

from mcp import ClientSession, StdioServerParameters
from mcp.client.sse import sse_client
from mcp.client.stdio import stdio_client
from pydantic import ConfigDict
from pydantic.dataclasses import dataclass

from igniteai_agent_sdk.tools.base_tool import Tool as AgentTool


@dataclass(config=ConfigDict(arbitrary_types_allowed=True))
class MCPServer:
    """Manages MCP server connections and tool execution."""

    name: str
    command: Optional[str] = None
    sse_url: Optional[str] = None
    args: Optional[list[str]] = None
    env: Optional[dict] = None
    _initialized: bool = False

    def __post_init__(self):
        self._session: Optional[ClientSession] = None
        self._lock = asyncio.Lock()
        self._server_task: Optional[asyncio.Task] = None
        self._tools: List[AgentTool] = []

    async def initialize(self) -> None:
        """Initialize the server connection."""
        if self._initialized:
            return

        async with self._lock:
            if self._initialized:
                return

            if (self.sse_url is not None and self.sse_url != "") and (self.command is not None and self.command != ""):
                raise ValueError("Provide only one of command or sse_url not both.")

            self._server_task = asyncio.create_task(self._run_server())
            try:
                await asyncio.wait_for(self._server_initialized(), timeout=30.0)
                self._initialized = True
            except asyncio.TimeoutError:
                await self.cleanup()
                raise TimeoutError(f"Timeout waiting for MCP server {self.name} to initialize") from None
            except Exception as e:
                await self.cleanup()
                raise ValueError(f"Failed to initialize MCP server {self.name}: {str(e)}") from None

    async def _server_initialized(self) -> None:
        """Wait for server to be initialized."""
        while self._session is None:
            await asyncio.sleep(0.1)

    async def _run_server(self) -> None:
        """Run the server in its own task with its own exit stack."""
        exit_stack = AsyncExitStack()

        try:
            if self.sse_url is not None and self.sse_url != "":
                client = sse_client(url=self.sse_url)
            else:
                _command = shutil.which("npx") if self.command == "npx" else self.command
                if _command is None or self.args is None:
                    raise ValueError("The command must be a valid string and cannot be None.")

                server_params = StdioServerParameters(
                    command=_command,
                    args=self.args,
                    env={**os.environ, **self.env} if self.env is not None else None,
                )
                client = stdio_client(server_params)

            streams = await exit_stack.enter_async_context(client)
            session = await exit_stack.enter_async_context(ClientSession(*streams))
            await session.initialize()
            self._session = session

            try:
                await asyncio.Future()
            finally:
                self._session = None
                await exit_stack.aclose()

        except Exception as e:
            print(f"Error in MCP server {self.name}: {e}")

            await exit_stack.aclose()
            raise

    async def cleanup(self) -> None:
        """Clean up server resources by canceling the server task."""
        async with self._lock:
            if self._server_task and not self._server_task.done():
                self._server_task.cancel()
                try:
                    await self._server_task
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    print(f"Error during MCP server {self.name} cleanup: {e}")
                finally:
                    self._server_task = None
                    self._session = None
                    self._initialized = False
                    self._tools.clear()

    async def list_tools(self) -> list[AgentTool]:
        """List tools from the server.

        Returns:
            A list of available tools.

        """
        if not self._initialized or self._session is None:
            raise RuntimeError(f"Server {self.name} not initialized")

        try:
            response = await self._session.list_tools()
            if len(response.tools) < 1:
                raise Exception(f"MCP Server {self.name} has no tools.")

            tools = []
            for mcp_tool in response.tools:
                tool = AgentTool.infer_from_mcp(mcp_tool, self._session)
                tools.append(tool)
                self._tools.append(tool)
            return tools
        except Exception as e:
            print(f"Error listing tools from MCP server {self.name}: {e}")
            await self.cleanup()
            raise
