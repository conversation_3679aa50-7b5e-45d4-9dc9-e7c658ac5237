import asyncio
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Set

if TYPE_CHECKING:
    from igniteai_agent_sdk.agent.base_agent import Agent

from pydantic import BaseModel, Field

from igniteai_agent_sdk._event_emitter import EventEmitter
from igniteai_agent_sdk.logger import root_logger
from igniteai_agent_sdk.tools.base_tool import Tool
from igniteai_agent_sdk.types import Event

logger = root_logger


class HandoffStatus(str, Enum):
    """Status of a task handoff to another agent."""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"


class AgentHandoffEvent(Event):
    """Event emitted during agent handoff process."""

    source_agent: str
    target_agent: str
    task_description: str
    status: HandoffStatus
    run_id: Optional[str] = None
    error: Optional[str] = None
    result: Optional[str] = None


class HandoffTask(BaseModel):
    """A task to be handed off to another agent."""

    task_description: str = Field(..., description="A clear and concise description of the task the agent should achieve")
    expected_output: str = Field(..., description="The expected format or content of the output")
    priority: int = Field(default=0, description="Priority level of the task (higher value = higher priority)")
    timeout_seconds: Optional[int] = Field(default=None, description="Maximum execution time in seconds")
    retry_count: int = Field(default=0, description="Number of times to retry on failure")


class HandoffResult(BaseModel):
    """Result of a handoff operation."""

    result: str = ""
    status: HandoffStatus = HandoffStatus.COMPLETED
    execution_time_ms: float = 0
    error: Optional[str] = None
    run_id: Optional[str] = None


class AgentHandoffManager:
    """Manager for coordinating task delegation between agents."""

    def __init__(self):
        """Initialize the handoff manager."""
        self.active_handoffs: Dict[str, HandoffTask] = {}
        self.completed_handoffs: Dict[str, HandoffResult] = {}
        self.event_emitter: EventEmitter = EventEmitter()
        self.worker_pool: Dict[str, Any] = {}

    def register_worker(self, agent: "Agent") -> None:
        """Register an agent as a worker.

        Args:
            agent: The agent instance to register

        """
        if self.worker_pool.get(agent.name, None) is None:
            self.worker_pool[agent.name] = agent
            logger.debug(f"Registered agent '{agent.name}' as a worker in HandoffManager")

    def register_workers(self, agents: List["Agent"]) -> None:
        """Register multiple agents as workers.

        Args:
            agents: List of agent instances to register

        """
        for agent in agents:
            self.register_worker(agent)

    def get_worker_names(self) -> Set[str]:
        """Get the names of all registered worker agents.

        Returns:
            Set of worker agent names

        """
        return set(self.worker_pool.keys())

    async def handoff_task(self, source_agent: "Agent", target_agent_name: str, task: HandoffTask) -> HandoffResult:
        """Hand off a task to another agent.

        Args:
            source_agent: The agent delegating the task
            target_agent_name: The name of the agent to delegate to
            task: The task to delegate

        Returns:
            HandoffResult containing the result of the handoff

        Raises:
            ValueError: If the target agent is not registered

        """
        start_time = datetime.now()
        handoff_id = f"handoff_{start_time.strftime('%Y%m%d%H%M%S')}_{source_agent.name}_to_{target_agent_name}"

        if target_agent_name not in self.worker_pool:
            error_msg = f"Target agent '{target_agent_name}' not registered in HandoffManager"
            logger.error(error_msg)

            await self.event_emitter.emit(
                AgentHandoffEvent(
                    source_agent=source_agent.name,
                    target_agent=target_agent_name,
                    task_description=task.task_description,
                    status=HandoffStatus.FAILED,
                    error=error_msg,
                    run_id=source_agent.run_id if hasattr(source_agent, "run_id") else None,
                )
            )

            return HandoffResult(
                status=HandoffStatus.FAILED,
                error=error_msg,
                run_id=source_agent.run_id if hasattr(source_agent, "run_id") else None,
            )

        target_agent = self.worker_pool[target_agent_name]
        self.active_handoffs[handoff_id] = task

        await self.event_emitter.emit(
            AgentHandoffEvent(
                source_agent=source_agent.name,
                target_agent=target_agent_name,
                task_description=task.task_description,
                status=HandoffStatus.PENDING,
                run_id=source_agent.run_id if hasattr(source_agent, "run_id") else None,
            )
        )

        try:
            message = f"{task.task_description}\n\nThe expected output is: {task.expected_output}"

            await self.event_emitter.emit(
                AgentHandoffEvent(
                    source_agent=source_agent.name,
                    target_agent=target_agent_name,
                    task_description=task.task_description,
                    status=HandoffStatus.IN_PROGRESS,
                    run_id=source_agent.run_id if hasattr(source_agent, "run_id") else None,
                )
            )

            if task.timeout_seconds:
                try:
                    run_response = await asyncio.wait_for(target_agent.run(message), timeout=task.timeout_seconds)
                except asyncio.TimeoutError:
                    raise TimeoutError(f"Task execution timed out after {task.timeout_seconds} seconds") from None
            else:
                run_response = await target_agent.run(message)

            run_response_text = run_response.final_response

            end_time = datetime.now()
            execution_time_ms = (end_time - start_time).total_seconds() * 1000

            result = HandoffResult(
                result=run_response_text or "No response from the agent.",
                status=HandoffStatus.COMPLETED,
                execution_time_ms=execution_time_ms,
                run_id=target_agent.run_id if hasattr(target_agent, "run_id") else None,
            )

            self.completed_handoffs[handoff_id] = result
            del self.active_handoffs[handoff_id]

            await self.event_emitter.emit(
                AgentHandoffEvent(
                    source_agent=source_agent.name,
                    target_agent=target_agent_name,
                    task_description=task.task_description,
                    status=HandoffStatus.COMPLETED,
                    result=run_response_text,
                    run_id=source_agent.run_id if hasattr(source_agent, "run_id") else None,
                )
            )

            return result

        except Exception as e:
            logger.error(
                f"Error during task handoff from '{source_agent.name}' to '{target_agent_name}': {str(e)}", exc_info=True
            )

            if task.retry_count > 0:
                logger.debug(
                    f"Retrying handoff from '{source_agent.name}' to '{target_agent_name}', {task.retry_count} attempts remaining"
                )
                task.retry_count -= 1
                return await self.handoff_task(source_agent, target_agent_name, task)

            error_result = HandoffResult(
                status=HandoffStatus.FAILED, error=str(e), run_id=source_agent.run_id if hasattr(source_agent, "run_id") else None
            )

            self.completed_handoffs[handoff_id] = error_result
            if handoff_id in self.active_handoffs:
                del self.active_handoffs[handoff_id]

            await self.event_emitter.emit(
                AgentHandoffEvent(
                    source_agent=source_agent.name,
                    target_agent=target_agent_name,
                    task_description=task.task_description,
                    status=HandoffStatus.FAILED,
                    error=str(e),
                    run_id=source_agent.run_id if hasattr(source_agent, "run_id") else None,
                )
            )

            return error_result

    def create_handoff_tools(self, agent: "Agent") -> List[Tool]:
        """Create handoff tools for the specified agent.

        Args:
            agent: The agent that will use these tools

        Returns:
            List of Tool objects for handling agent handoffs

        """
        from igniteai_agent_sdk.tools.base_tool import Tool

        tools = []

        for worker_name, _ in self.worker_pool.items():
            if worker_name == agent.name:
                continue

            async def _handoff_to_agent(
                task_description: str,
                expected_output: str,
                priority: int = 0,
                timeout_seconds: Optional[int] = None,
                retry_count: int = 0,
                worker_name: str = worker_name,
            ) -> str:
                """Delegate a task to another agent.

                Args:
                    task_description: Clear description of what the agent should do
                    expected_output: The format or content expected as output
                    priority: Task priority (higher = more important)
                    timeout_seconds: Maximum time allowed for execution
                    retry_count: Number of times to retry on failure
                    worker_name: Name of the worker agent to delegate to

                Returns:
                    The result from the delegated agent

                """
                task = HandoffTask(
                    task_description=task_description,
                    expected_output=expected_output,
                    priority=priority,
                    timeout_seconds=timeout_seconds,
                    retry_count=retry_count,
                )

                result = await self.handoff_task(agent, worker_name, task)

                if result.status == HandoffStatus.FAILED:
                    return f"Error from {worker_name}: {result.error or 'Unknown error'}"

                return result.result

            handoff_func_name = f"transfer_to_{worker_name.lower().replace(' ', '_')}"

            description = f"""Use this function to delegate a task to the {worker_name} agent.
You must provide a clear and concise description of what the agent should do and the expected output format.

Args:
    task_description (str): A clear and concise description of what the agent should do.
    expected_output (str): The format or content you expect to receive back.
    priority (int, optional): Priority level of the task (higher = more important). Default: 0.
    timeout_seconds (int, optional): Maximum execution time in seconds. Default: None (no timeout).
    retry_count (int, optional): Number of times to retry on failure. Default: 0.
    worker_name (str): Name of the worker agent to delegate to

Returns:
    str: The result from the delegated agent.
"""

            tool = Tool.infer_from_callable(func=_handoff_to_agent, tool_name=handoff_func_name, description=description)

            tools.append(tool)

        return tools


handoff_manager = AgentHandoffManager()


def get_handoff_tools(agent: "Agent") -> List[Tool]:
    """Get handoff tools for the specified agent.

    This is a convenience function to access the singleton handoff_manager.

    Args:
        agent: The agent that will use these tools

    Returns:
        List of Tool objects for handling agent handoffs

    """
    return handoff_manager.create_handoff_tools(agent)
