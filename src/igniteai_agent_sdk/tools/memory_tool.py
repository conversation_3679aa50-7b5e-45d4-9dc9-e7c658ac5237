from typing import TYPE_CHECKING, Any, Dict, List, Optional

if TYPE_CHECKING:
    from igniteai_agent_sdk.agent.base_agent import Agent

from igniteai_agent_sdk.tools.base_tool import Tool


def get_memory_tools(agent: "Agent") -> List[Tool]:
    async def remember(
        content: str, key: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None, run_id: Optional[str] = None
    ) -> str:
        """Store information in the agent's memory.

        Args:
            content: Information to remember
            key: Optional key for storing the information
            metadata: Optional metadata to associate with the memory
            run_id: Optional run ID to associate with the memory

        Returns:
            The key where the information is stored

        """
        memory_key = await agent.remember(content, key, metadata, run_id)
        return f"Successfully stored information in memory with key: {memory_key}" if memory_key else "Memory is not enabled"

    async def search_memory(query: str, limit: int = 5, run_id: Optional[str] = None) -> str:
        """Search the agent's memory for relevant information.

        Args:
            query: Search query
            limit: Maximum number of results to return
            run_id: Optional run ID to filter results by

        Returns:
            String representation of the search results

        """
        results = await agent.recall_by_query(query, limit, run_id)

        if not results:
            return "No memories found matching your query or memory is not enabled"

        output = f"Found {len(results)} memories related to '{query}'"
        if run_id:
            output += f" for run: {run_id}"
        output += ":\n"

        for idx, memory in enumerate(results, 1):
            output += f"{idx}. {memory['value']}\n"

        return output

    async def get_recent_memories(limit: int = 5, run_id: Optional[str] = None) -> str:
        """Retrieve the agent's most recent memories.

        Args:
            limit: Maximum number of memories to retrieve
            run_id: Optional run ID to filter by

        Returns:
            String representation of recent memories

        """
        results = await agent.recall_recent(limit, run_id)

        if not results:
            return "No recent memories found or memory is not enabled"

        output = f"Recent memories (showing {len(results)})"
        if run_id:
            output += f" for run: {run_id}"
        output += ":\n"

        for idx, memory in enumerate(results, 1):
            output += f"{idx}. {memory['value']}\n"

        return output

    async def get_memories_by_run(run_id: Optional[str] = None, limit: int = 50) -> str:
        """Retrieve memories for a specific run.

        Args:
            run_id: Run ID to retrieve memories for (defaults to current run)
            limit: Maximum number of memories to retrieve

        Returns:
            String representation of memories for the run

        """
        results = await agent.recall_by_run(run_id, limit)

        if not results:
            return f"No memories found for run ID: {run_id or 'current run'} or memory is not enabled"

        output = f"Memories for run {run_id or 'current run'} (showing {len(results)}):\n"

        for idx, memory in enumerate(results, 1):
            output += f"{idx}. {memory['value']}\n"

        return output

    return [
        Tool.infer_from_callable(func=remember),
        Tool.infer_from_callable(func=search_memory),
        Tool.infer_from_callable(func=get_recent_memories),
        Tool.infer_from_callable(func=get_memories_by_run),
    ]
