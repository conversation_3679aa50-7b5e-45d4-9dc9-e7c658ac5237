import asyncio
import inspect
import json
from datetime import datetime
from time import time
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Union

if TYPE_CHECKING:
    from igniteai_agent_sdk.agent.base_agent import Agent

from pydantic import ConfigDict, Field
from pydantic.dataclasses import dataclass

from igniteai_agent_sdk._agent_context import Context
from igniteai_agent_sdk.llm.types import ChatCompletionMessage as Message
from igniteai_agent_sdk.logger import root_logger
from igniteai_agent_sdk.tools.base_tool import BaseTool, Tool, wrap
from igniteai_agent_sdk.types import Result, ToolCallEvent, ToolResponse

logger = root_logger


@dataclass(config=ConfigDict(arbitrary_types_allowed=True))
class ToolKit:
    tools: Optional[list[Union[Callable, Tool, BaseTool]]] = None
    _schema_cache: Dict[str, Any] = Field(default_factory=dict)

    def __post_init__(self):
        self._tools: dict[str, Tool] = dict[str, Tool]()
        if self.tools is not None:
            for _tool in self.tools:
                self._add_tool(_tool)

    def _add_tool(self, tool: Union[Callable, Tool, BaseTool]):
        """Add a tool to the toolkit.

        Args:
            tool: A callable, Tool instance, or BaseTool (like LangChain tool)

        """
        if not isinstance(tool, Tool):
            try:
                tool = wrap(tool)
            except Exception as e:
                logger.error(f"Failed to wrap tool: {str(e)}")
                raise ValueError(f"Failed to wrap tool: {str(e)}") from e

        self._tools[tool.name] = tool

        if tool.name in self._schema_cache:
            del self._schema_cache[tool.name]

    def _add_tools(self, tools: list[Union[Callable, Tool, BaseTool]]):
        """Add multiple tools to the toolkit.

        Args:
            tools: List of callables, Tool instances, or BaseTools

        """
        for _tool in tools:
            self._add_tool(_tool)

    def get_tools(self) -> dict:
        """Return tools in the specified format (default OpenAI).

        Returns:
            Dictionary of tools keyed by name

        """
        return self._tools

    def get_json_schema(self, tool_names: Optional[List[str]] = None) -> list:
        """Convert tools to OpenAI's format.

        Args:
            tool_names: Optional list of tool names to include (all if None)

        Returns:
            List of tool specifications in OpenAI format

        """
        spec = []
        if tool_names is None:
            tool_names = list(self._tools.keys())

        for name in tool_names:
            if (tool := self._tools.get(name, None)) is None:
                continue

            cached_schema = self._schema_cache.get(name)
            if cached_schema:
                spec.append(cached_schema)
                continue

            tool_schema = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.param_model.model_json_schema(),
                },
            }

            self._schema_cache[name] = tool_schema
            spec.append(tool_schema)

        return spec

    def get_string_schema(self, tool_names: Optional[List[str]] = None) -> str:
        """Convert tools schema to string.

        Args:
            tool_names: Optional list of tool names to include (all if None)

        Returns:
            JSON string representation of tools schema

        """
        return json.dumps(self.get_json_schema(tool_names), indent=2)

    def handle_tool_result(self, result: Any) -> Result:
        """Handle various result types from tools.

        Args:
            result: The raw result from a tool execution

        Returns:
            A standardized Result object

        Raises:
            TypeError: If the result can't be converted to a Result

        """
        if isinstance(result, Result):
            return result
        elif hasattr(result, "name") and hasattr(result, "run_id"):
            return Result(
                value=json.dumps({"assistant": result.name}),
                agent=result,
            )
        else:
            try:
                return Result(value=str(result))
            except Exception as e:
                error_message = f"Failed to cast response to string: {result}. Make sure agent functions return a string or Result object. Error: {str(e)}"
                logger.error(error_message)
                raise TypeError(error_message) from e

    async def _execute_single_tool(
        self, tool_call: Any, context: Optional[Context] = None, agent: Optional["Agent"] = None
    ) -> tuple[Result, Message]:
        """Execute a single tool and return its result and message.

        Args:
            tool_call: The tool call to execute
            context: Optional context for the tool
            agent: The agent executing the tool

        Returns:
            A tuple of (Result, Message)

        Raises:
            ValueError: If the tool isn't registered or other errors occur

        """
        if isinstance(tool_call, dict):
            tool_name = tool_call.get("function", {}).get("name", "")
            arguments = tool_call.get("function", {}).get("arguments", "{}")
            tool_call_id = tool_call.get("id", "unknown")
        else:
            tool_name = getattr(tool_call.function, "name", "")
            arguments = getattr(tool_call.function, "arguments", "{}")
            tool_call_id = getattr(tool_call, "id", "unknown")

        if isinstance(arguments, str):
            try:
                arguments = json.loads(arguments)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in tool arguments: {arguments}. Error: {str(e)}")
                return (
                    Result(value=f"Error: Invalid JSON arguments - {str(e)}"),
                    Message(
                        role="tool",  # type: ignore
                        name=tool_name,
                        content=f"Error: Invalid JSON arguments - {str(e)}",
                        tool_call_id=tool_call_id,
                    ),
                )

        if tool_name not in self._tools:
            error_msg = f"Tool '{tool_name}' not registered."
            logger.error(error_msg)
            raise ValueError(error_msg)

        tool = self._tools[tool_name]
        tool_func = tool._function
        param_model = tool.param_model

        try:
            try:
                validated_args = param_model(**arguments).model_dump()
            except Exception as e:
                logger.error(f"Error validating arguments for tool '{tool_name}': {str(e)}")
                return (
                    Result(value=f"Error validating arguments: {str(e)}"),
                    Message(
                        role="tool",  # type: ignore
                        name=tool_name,
                        content=f"Error validating arguments: {str(e)}",
                        tool_call_id=tool_call_id,
                    ),
                )

            if tool.requires_context:
                if context is None:
                    error_msg = f"Context required for tool '{tool_name}'"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                func_signature = inspect.signature(tool_func)
                for pname, pinfo in func_signature.parameters.items():
                    if pinfo.annotation is Context:
                        validated_args[pname] = context
                        break

            tool_start_time = time()
            tool_start_dt = datetime.now()

            if agent and hasattr(agent, "usage_tracker"):
                agent.usage_tracker.start_tool_call(tool_name=tool_name, args=validated_args)

            if inspect.iscoroutinefunction(tool_func):
                result = await tool_func(**validated_args)
            else:
                result = tool_func(**validated_args)

            tool_end_time = time()
            tool_end_dt = datetime.now()
            tool_duration_ms = (tool_end_time - tool_start_time) * 1000

            if agent is not None and hasattr(agent, "event_emitter"):
                await agent.event_emitter.emit(
                    ToolCallEvent(
                        agent_name=agent.name,
                        tool_name=tool.name,
                        arguments=str(arguments),
                        run_id=agent.run_id,
                        start_time=tool_start_dt,
                        end_time=tool_end_dt,
                        duration_ms=tool_duration_ms,
                    )
                )

            if agent and hasattr(agent, "usage_tracker"):
                agent.usage_tracker.end_tool_call(tool_name, duration_ms=tool_duration_ms)

            result = self.handle_tool_result(result)

            return result, Message(role="tool", name=tool_name, content=result.value, tool_call_id=tool_call_id)  # type: ignore

        except Exception as e:
            logger.error(f"Error executing tool '{tool_name}': {str(e)}", exc_info=True)

            error_result = Result(value=f"Error executing tool: {str(e)}")
            error_message = Message(
                role="tool",  # type: ignore
                name=tool_name,
                content=f"Error executing tool: {str(e)}",
                tool_call_id=tool_call_id,
            )

            return error_result, error_message

    async def execute_tools(
        self,
        tool_calls: list[Any],
        agent: Optional["Agent"] = None,
        context: Optional[Context] = None,
    ) -> ToolResponse:
        """Executes registered tools based on the tool calls from the model.

        Args:
            tool_calls: List of tool calls from the model
            agent: The calling agent
            context: Optional context

        Returns:
            ToolResponse containing results and messages for each tool call

        """
        tool_response = ToolResponse()
        tool_response.agent_name = agent.name if agent and hasattr(agent, "name") else None

        if not isinstance(tool_calls, list):
            tool_calls = [tool_calls]

        tasks = []
        for tool_call in tool_calls:
            tasks.append(self._execute_single_tool(tool_call, context, agent))

        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"Tool execution error: {str(result)}")
                    error_result = Result(value=f"Error: {str(result)}")
                    error_message = Message(role="tool", name="error", content=f"Error: {str(result)}", tool_call_id="error")  # type: ignore
                    tool_response.results.append(error_result)
                    tool_response.messages.append(error_message)
                else:
                    result_obj, message = result  # type: ignore
                    tool_response.results.append(result_obj)
                    tool_response.messages.append(message)

        except Exception as e:
            logger.error(f"Unexpected error executing tools: {str(e)}", exc_info=True)
            error_result = Result(value=f"Error executing tools: {str(e)}")
            error_message = Message(role="tool", name="error", content=f"Error executing tools: {str(e)}", tool_call_id="error")  # type: ignore
            tool_response.results.append(error_result)
            tool_response.messages.append(error_message)

        return tool_response
