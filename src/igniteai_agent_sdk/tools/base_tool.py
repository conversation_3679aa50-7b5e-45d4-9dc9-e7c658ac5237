import inspect
from typing import (
    Any,
    Callable,
    Dict,
    Optional,
    Protocol,
    Type,
    TypeVar,
    Union,
    runtime_checkable,
)

from docstring_parser import parse
from mcp import ClientSession
from mcp.types import CallToolResult, TextContent
from mcp.types import Tool as MCPTool
from pydantic import BaseModel, Field, create_model
from pydantic.dataclasses import dataclass
from pydantic.json_schema import JsonSchemaValue, models_json_schema

from igniteai_agent_sdk._agent_context import Context
from igniteai_agent_sdk.logger import root_logger

logger = root_logger

ToolCallable = TypeVar("ToolCallable", bound=Callable[..., Any])


@runtime_checkable
class BaseTool(Protocol):
    """Protocol for compatibility with external tool frameworks like LangChain."""

    name: str
    description: str

    def __call__(self, *args: Any, **kwargs: Any) -> Any: ...


def extract_param_descriptions(func: Optional[Callable] = None, description: Optional[str] = None) -> dict[str, str]:
    """Extract parameter descriptions from function docstring.

    Args:
        func: The function to extract parameter descriptions from
        description: Optional description of the function
    Returns:
        Dictionary mapping parameter names to their descriptions

    """
    docstring = description if description is not None else inspect.getdoc(func) or ""
    parsed_docstring = parse(docstring)

    param_descriptions = {}
    for param in parsed_docstring.params:
        param_descriptions[param.arg_name] = param.description or ""

    return param_descriptions


def convert_to_tool_spec(func: Callable, param_model: Type[BaseModel]) -> Dict[str, Any]:
    """Convert the function and its Pydantic model to a unified tool specification."""
    type_mapping = {str: "string", int: "integer", float: "number", bool: "boolean"}

    properties = {}
    for field_name, field in param_model.model_fields.items():
        field_type = field.annotation

        if hasattr(field_type, "__members__"):
            enum_values = [member.value if hasattr(member, "value") else member.name for member in field_type]  # type: ignore
            properties[field_name] = {
                "type": "string",
                "enum": enum_values,
                "description": field.description or "",
            }

            if str(field.default) != "PydanticUndefined":
                properties[field_name]["default"] = field.default.value if hasattr(field.default, "value") else field.default
        else:
            properties[field_name] = {
                "type": type_mapping.get(field_type, str(field_type)),
                "description": field.description or "",
            }

            if str(field.default) != "PydanticUndefined":
                properties[field_name]["default"] = field.default

    return {
        "name": func.__name__,
        "description": func.__doc__ or "",
        "parameters": {
            "type": "object",
            "properties": properties,
            "required": [
                name
                for name, field in param_model.model_fields.items()
                if field.is_required and str(field.default) == "PydanticUndefined"
            ],
        },
    }


@dataclass
class Tool:
    name: str
    description: str
    _function: Callable
    parameters: Dict[str, Any]
    param_model: Type[BaseModel]
    requires_context: bool

    _source: Optional[str] = None
    _metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self._metadata is None:
            self._metadata = {}

    def __call__(self, *args: Any, **kwargs: Any) -> Any:
        return self._function(*args, **kwargs)

    @property
    def json_schema(self, title: str | None = None) -> JsonSchemaValue:
        _, top_level_schema = models_json_schema(
            [(self.param_model, "validation")],
            title=title or self.name,
        )
        return top_level_schema

    @classmethod
    def infer_from_callable(
        cls, func: Callable, tool_name: Optional[str] = None, description: Optional[str] = None, ignore_validations: bool = False
    ) -> "Tool":
        """Infer parameters(required and optional) and requirements directly from the function signature."""
        signature = inspect.signature(func)
        fields = {}
        required_fields = []
        requires_context = False

        param_descriptions = extract_param_descriptions(func, description)

        docstring = description if description is not None and description else inspect.getdoc(func) or ""

        parsed_docstring = parse(docstring)
        function_description = parsed_docstring.short_description or ""

        if parsed_docstring.long_description:
            function_description += "\n\n" + parsed_docstring.long_description

        for param_name, param in signature.parameters.items():
            if param.annotation == inspect._empty:
                raise TypeError(f"Parameter '{param_name}' in function '{func.__name__}' must have a type annotation.")

            if param.annotation is Context:
                requires_context = True
                continue

            param_type = param.annotation
            if (description := param_descriptions.get(param_name, None)) is None and not ignore_validations:
                raise TypeError(f"Parameter '{param_name}' in function '{func.__name__}' must have a description.")

            if param.default == inspect._empty:
                fields[param_name] = (param_type, Field(..., description=description))
                required_fields.append(param_name)
            else:
                fields[param_name] = (
                    param_type,
                    Field(default=param.default, description=description),
                )

        param_model = create_model(f"{func.__name__.capitalize()}Params", **fields)

        tool_spec = convert_to_tool_spec(func, param_model)

        return cls(
            name=tool_name if tool_name is not None and tool_name else tool_spec["name"],
            description=function_description,
            _function=func,
            parameters=tool_spec["parameters"],
            param_model=param_model,
            requires_context=requires_context,
            _source="custom",
        )

    @classmethod
    def from_langchain_tool(cls, langchain_tool: BaseTool) -> "Tool":
        """Convert a LangChain Tool into an ignite-ai Tool.

        Args:
            langchain_tool: A LangChain tool instance

        Returns:
            An ignite-ai Tool instance

        """
        logger.debug(f"Converting LangChain tool '{langchain_tool.name}' to ignite-ai tool")

        name = getattr(langchain_tool, "name", None)
        description = getattr(langchain_tool, "description", None)

        if not name:
            name = langchain_tool.__class__.__name__
        if not description:
            description = inspect.getdoc(langchain_tool) or "No description available"

        async def wrapper_func(**args) -> str:
            """Wrapper function for LangChain tool.

            Args:
                args: The input to the tool

            Returns:
                The output from the tool

            """
            try:
                result = await langchain_tool._arun(**args)  # type: ignore

                if not isinstance(result, str):
                    result = str(result)

                return result
            except Exception as e:
                logger.error(f"Error executing LangChain tool '{name}': {str(e)}")
                return f"Error: {str(e)}"

        param_model = langchain_tool.get_input_schema()  # type: ignore

        return cls(
            name=name,
            description=description,
            _function=wrapper_func,
            parameters=langchain_tool.get_input_jsonschema(),  # type: ignore
            param_model=param_model,
            requires_context=False,
            _source="langchain",
            _metadata={"original_tool": langchain_tool.__class__.__name__},
        )

    @classmethod
    def infer_from_mcp(cls, mcp_tool: MCPTool, session: ClientSession) -> "Tool":
        """Convert an MCP tool (with attributes 'name', 'description', 'inputSchema') into
        an instance of the Tool class.

        The function creates a Pydantic model based on the inputSchema, mapping JSON schema
        types to Python types. It also attaches a dummy function that you can later replace
        with the actual logic for calling the MCP server.
        """
        input_schema = mcp_tool.inputSchema
        type_map = {"integer": int, "number": float, "string": str, "boolean": bool}

        param_descriptions = extract_param_descriptions(description=mcp_tool.description or "")
        docstring = mcp_tool.description or ""

        parsed_docstring = parse(docstring)
        function_description = parsed_docstring.short_description or ""

        if parsed_docstring.long_description:
            function_description += "\n\n" + parsed_docstring.long_description

        fields = {}
        for field_name, field_spec in input_schema.get("properties", {}).items():
            json_type = field_spec.get("type", "string")
            py_type = type_map.get(json_type, str)
            default = ... if field_name in input_schema.get("required", []) else None

            fields[field_name] = (py_type, Field(default=default, description=param_descriptions.get(field_name, "")))

        param_model = create_model(f"{mcp_tool.name}_Params", **fields)

        async def _call_tool(**tool_args):
            try:
                logger.debug(f"Calling MCP Tool '{mcp_tool.name}' with args: {tool_args}")

                result: CallToolResult = await session.call_tool(mcp_tool.name, tool_args)

                if result.isError:
                    raise Exception(f"Error from MCP tool '{mcp_tool.name}': {result.content}")

                response_str = ""
                for content_item in result.content:
                    if isinstance(content_item, TextContent):
                        response_str += content_item.text + "\n"
                    else:
                        response_str += f"[Unsupported content type: {content_item.type}]\n"
                return response_str.strip()
            except Exception as e:
                logger.error(f"Failed to call MCP tool '{mcp_tool.name}': {e}")
                return f"Error: {e}"

        return cls(
            name=mcp_tool.name,
            description=function_description or "",
            _function=_call_tool,
            parameters=input_schema,
            param_model=param_model,
            requires_context=False,
            _source="mcp",
            _metadata={"schema": input_schema},
        )


def wrap(function_or_tool: Union[Callable, BaseTool]) -> Tool:
    """Create a Tool instance from a callable function or an existing tool.

    Args:
        function_or_tool: A callable function or a BaseTool instance

    Returns:
        A Tool instance

    Raises:
        TypeError: If the input isn't a callable or a BaseTool

    """
    if isinstance(function_or_tool, BaseTool):
        return Tool.from_langchain_tool(function_or_tool)
    elif callable(function_or_tool):
        return Tool.infer_from_callable(function_or_tool)
    else:
        raise TypeError(f"Expected callable or BaseTool instance, got {type(function_or_tool)}")
