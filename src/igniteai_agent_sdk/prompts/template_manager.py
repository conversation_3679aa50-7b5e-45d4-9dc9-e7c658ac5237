"""Prompt template manager for handling and customizing prompt templates."""

import os
from typing import Any, Dict, Optional


class PromptTemplate:
    """A class representing a template for prompts with variable substitution."""

    def __init__(self, template: str, template_type: str = "default", metadata: Optional[Dict[str, Any]] = None):
        """Initialize a prompt template.

        Args:
            template: The template string with placeholders like {variable_name}
            template_type: The type of template (e.g., "react", "base", "chat")
            metadata: Additional metadata about the template

        """
        self.template = template
        self.template_type = template_type
        self.metadata = metadata or {}

    def format(self, **kwargs) -> str:
        """Format the template with the provided variables.

        Args:
            **kwargs: Variables to substitute in the template

        Returns:
            The formatted template string

        """
        try:
            return self.template.format(**kwargs)
        except KeyError as e:
            missing_key = str(e).strip("'")
            raise ValueError(f"Missing required template variable: {missing_key}") from e
        except Exception as e:
            raise ValueError(f"Error formatting template: {str(e)}") from e


class PromptTemplateManager:
    """Manager for loading, storing, and retrieving prompt templates."""

    def __init__(self, templates_dir: Optional[str] = None):
        """Initialize the prompt template manager.

        Args:
            templates_dir: Optional directory to load templates from

        """
        self._templates: Dict[str, PromptTemplate] = {}
        self.templates_dir = templates_dir

        self._register_builtin_templates()

        if templates_dir and os.path.exists(templates_dir):
            self.load_templates_from_directory(templates_dir)

    def _register_builtin_templates(self) -> None:
        """Register the built-in templates."""
        from igniteai_agent_sdk.prompts.react_prompt import REACTPROMPT

        self.register_template(
            "react",
            PromptTemplate(
                template=REACTPROMPT, template_type="react", metadata={"description": "Standard ReAct prompt template"}
            ),
        )

        base_template = """You are a helpful AI assistant designed to help the user from answering questions to variety of tasks.

**Introduction**: {introduction}

**Role**: You are a {role}. Assume the skills, knowledge, and voice of this role.

**Goal**: Your main goal is to {task}

{additional_context}

{transfer_prompt}

{instructions}

"""

        self.register_template(
            "base",
            PromptTemplate(
                template=base_template, template_type="base", metadata={"description": "Standard base agent prompt template"}
            ),
        )

    def register_template(self, name: str, template: PromptTemplate) -> None:
        """Register a template with a name.

        Args:
            name: Name to register the template under
            template: PromptTemplate instance

        """
        self._templates[name] = template

    def get_template(self, name: str) -> PromptTemplate:
        """Get a template by name.

        Args:
            name: Name of the template

        Returns:
            The template

        Raises:
            KeyError: If the template doesn't exist

        """
        if name not in self._templates:
            raise KeyError(f"Template '{name}' not found")

        return self._templates[name]

    def format_template(self, template_name: str, **kwargs) -> str:
        """Format a template with variables.

        Args:
            template_name: Name of the template to format
            **kwargs: Variables to substitute in the template

        Returns:
            The formatted template string

        """
        template = self.get_template(template_name)
        return template.format(**kwargs)

    def add_template_from_string(
        self, name: str, template_string: str, template_type: str = "custom", metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add a new template from a string.

        Args:
            name: Name to register the template under
            template_string: The template string
            template_type: The type of template
            metadata: Additional metadata about the template

        """
        template = PromptTemplate(template=template_string, template_type=template_type, metadata=metadata or {})
        self.register_template(name, template)

    def load_templates_from_directory(self, directory_path: str) -> None:
        """Load templates from a directory.

        Args:
            directory_path: Path to directory containing template files

        """
        pass


template_manager = PromptTemplateManager()
