from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_serializer


class TokenUsage(BaseModel):
    """Tracking information for token usage during a run."""

    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0

    def update(self, prompt_tokens: int = 0, completion_tokens: int = 0) -> None:
        """Update token usage.

        Args:
            prompt_tokens: Number of prompt tokens used
            completion_tokens: Number of completion tokens used

        """
        self.prompt_tokens += prompt_tokens
        self.completion_tokens += completion_tokens
        self.total_tokens = self.prompt_tokens + self.completion_tokens


class ToolCall(BaseModel):
    """Information about a tool call."""

    tool_name: str
    duration_ms: float
    args: Dict[str, Any] = Field(default_factory=dict)


class Usage(BaseModel):
    """Usage information for an agent run."""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_ms: float = 0
    llm_call_count: int = 0
    tool_call_count: int = 0
    tokens: TokenUsage = Field(default_factory=TokenUsage)
    llm_models_used: List[str] = Field(default_factory=list)
    tool_calls: Dict[str, ToolCall] = Field(default_factory=dict)

    @field_serializer("start_time", "end_time")
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        if dt is None:
            return None
        return dt.isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """Convert usage information to a dictionary."""
        return {
            "duration_ms": self.duration_ms,
            "llm_call_count": self.llm_call_count,
            "tool_call_count": self.tool_call_count,
            "tokens": {
                "prompt_tokens": self.tokens.prompt_tokens,
                "completion_tokens": self.tokens.completion_tokens,
                "total_tokens": self.tokens.total_tokens,
            },
            "llm_models_used": self.llm_models_used,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
        }


class UsageTracker:
    """Tracks usage information during an agent run."""

    def __init__(self):
        self.reset()

    def reset(self) -> None:
        """Reset the usage tracker."""
        self.usage = Usage()
        self.usage.start_time = datetime.now()

    def start_llm_call(self, model: str) -> None:
        """Record the start of an LLM call.

        Args:
            model: The name of the LLM model being used

        """
        self.usage.llm_call_count += 1
        if model not in self.usage.llm_models_used:
            self.usage.llm_models_used.append(model)

    def update_token_usage(self, prompt_tokens: int = 0, completion_tokens: int = 0) -> None:
        """Update token usage information.

        Args:
            prompt_tokens: Number of prompt tokens used
            completion_tokens: Number of completion tokens used

        """
        self.usage.tokens.update(prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)

    def start_tool_call(self, tool_name: str, args: Dict[str, Any]):
        """Record the start of a tool call and return the tool call index.

        Args:
            tool_name: Name of the tool being called
            args: Arguments passed to the tool

        """
        self.usage.tool_call_count += 1
        self.usage.tool_calls[tool_name] = ToolCall(tool_name=tool_name, args=args, duration_ms=0)

    def end_tool_call(self, tool_name: str, duration_ms: float) -> None:
        """Record the end of a tool call.

        Args:
            tool_name: The name of the tool
            duration_ms: Duration of the tool call in milliseconds
            result_summary: Optional summary of the tool call result

        """
        if tool_name in self.usage.tool_calls:
            self.usage.tool_calls[tool_name].duration_ms = duration_ms

    def finish(self) -> Usage:
        """Finish tracking and return the usage information.

        Returns:
            Usage information collected during the run

        """
        self.usage.end_time = datetime.now()
        if self.usage.start_time:
            self.usage.duration_ms = (self.usage.end_time - self.usage.start_time).total_seconds() * 1000

        return self.usage
