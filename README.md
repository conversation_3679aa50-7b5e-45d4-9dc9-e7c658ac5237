# IgniteAI Agent SDK

A powerful and flexible framework for building AI agents and workflows with support for multiple LLM providers, tool integration, memory systems, and advanced agent patterns.

## ✨ Features

- **Multi-Provider LLM Support**: OpenAI, Groq, and Ollama integration
- **ReAct Agents**: Reasoning and Acting pattern implementation
- **Tool Integration**: Easy tool creation and management with ToolKit system
- **Memory Systems**: Persistent memory with file-based storage
- **Agent Handoff**: Coordinate multiple specialized agents
- **Workflow Engine**: Build complex multi-step workflows
- **MCP Protocol**: Model Context Protocol for external integrations
- **Guardrails**: Input/output validation and safety measures
- **Telemetry & Usage Tracking**: Monitor agent performance and costs
- **Event System**: Real-time event emission and handling
- **Context Management**: Shared context across agent interactions
- **Configuration Management**: Environment-based settings
- **Streaming Responses**: Real-time streaming of agent responses with events

## 🚀 Quick Start

### Installation

```bash
pip install igniteai-agent-sdk
```

### Basic Agent

```python
import asyncio
from igniteai_agent_sdk.agent import Agent

# Create a simple agent
agent = Agent(
    name="Assistant",
    introduction="You are a helpful AI assistant",
    task="Help users with their questions"
)

async def main():
    response = await agent.run("What is artificial intelligence?")
    print(response.final_response)

asyncio.run(main())
```

### ReAct Agent with Tools

```python
import asyncio
from igniteai_agent_sdk.agent import ReActAgent
from igniteai_agent_sdk.tools import ToolKit

def calculate(expression: str) -> str:
    """Calculate mathematical expressions."""
    try:
        result = eval(expression)
        return f"The result is: {result}"
    except Exception as e:
        return f"Error: {str(e)}"

agent = ReActAgent(
    name="MathBot",
    introduction="I'm a math assistant that can solve calculations",
    task="Help users with mathematical problems",
    tools=ToolKit(tools=[calculate]),
    verbose=True
)

async def main():
    response = await agent.run("What is 15 * 23 + 45?")
    print(response.final_response)

asyncio.run(main())
```

### Streaming Responses

Stream agent responses in real-time for better user experience:

```python
import asyncio
from igniteai_agent_sdk.agent import Agent

agent = Agent(
    name="StreamingBot",
    introduction="You are a helpful AI assistant",
    task="Provide streaming responses"
)

async def main():
    print("Assistant: ", end="", flush=True)
    
    async for chunk in agent.run_stream("Tell me about artificial intelligence"):
        if chunk.delta:
            print(chunk.delta, end="", flush=True)
        
        if chunk.is_final:
            print(f"\n[Completed in {chunk.chunk_index + 1} chunks]")
            break

asyncio.run(main())
```

## 📚 Core Components

### Agents

The SDK provides several agent types:

- **Agent**: Base agent class with full customization
- **ReActAgent**: Implements Reasoning and Acting pattern for complex tasks

### Tools

Tools extend agent capabilities:

```python
from igniteai_agent_sdk.tools import Tool, ToolKit, wrap

# Function-based tool
def get_weather(city: str) -> str:
    """Get weather for a city."""
    return f"Weather in {city}: Sunny, 72°F"

# Class-based tool
@wrap
class DatabaseTool(Tool):
    def query(self, sql: str) -> str:
        """Execute SQL query."""
        return f"Query results for: {sql}"

toolkit = ToolKit(tools=[get_weather, DatabaseTool()])
```

### Memory

Persistent memory across conversations:

```python
from igniteai_agent_sdk.agent import Agent

agent = Agent(
    name="MemoryBot",
    memory_enabled=True,
    memory_type="file",
    memory_config={"directory": "./memory", "filename": "agent_memory.json"}
)

# Agent will remember previous interactions
response1 = await agent.run("My name is Alice")
response2 = await agent.run("What's my name?")  # Will remember Alice
```

### Workflows

Build complex multi-step processes:

```python
from igniteai_agent_sdk.workflow import Workflow
from igniteai_agent_sdk.types import WorkflowStartEvent, WorkflowStopEvent

workflow = Workflow(name="Data Processing")

@workflow.task(WorkflowStartEvent)
def process_data(event):
    # Process incoming data
    return ProcessedEvent(data=event.data)

@workflow.task(ProcessedEvent)
async def finalize(event):
    # Final processing step
    return WorkflowStopEvent(result=event.data)

result = await workflow.start({"data": [1, 2, 3, 4, 5]})
```

### Agent Handoff

Coordinate multiple specialized agents:

```python
from igniteai_agent_sdk.agent import Agent, ReActAgent

# Specialized agents
math_agent = Agent(
    name="Math Expert",
    task="Solve mathematical problems",
    tools=[calculate]
)

research_agent = Agent(
    name="Research Expert",
    task="Find and summarize information",
    tools=[search_tool]
)

# Coordinator agent
coordinator = ReActAgent(
    name="Coordinator",
    task="Delegate tasks to appropriate experts",
    agents=[math_agent, research_agent]
)

response = await coordinator.run("Calculate 15% of 200 and research the history of percentages")
```

## 🔧 Configuration

### Environment Variables

Create a `.ignite-ai.env` file or set environment variables:

```bash
IGNITE_AI_DEFAULT_MODEL=openai:gpt-4o
IGNITE_AI_LLM_API_KEY=your-api-key
IGNITE_AI_LLM_BASE_URL=https://api.openai.com/v1
IGNITE_AI_LLM_TEMPERATURE=0.7
IGNITE_AI_MEMORY_DIR=./output/memory
```

### Programmatic Configuration

```python
from igniteai_agent_sdk.config import settings

# Access configuration
print(settings.DEFAULT_MODEL)
print(settings.LLM_TEMPERATURE)
```

## 🔌 LLM Providers

### OpenAI

```python
agent = Agent(
    name="OpenAI Agent",
    model="openai:gpt-4o",
    llm_provider_config={
        "api_key": "your-api-key"
    }
)
```

### Groq

```python
agent = Agent(
    name="Groq Agent",
    model="groq:llama-3.1-70b-versatile",
    llm_provider_config={
        "api_key": "your-groq-api-key"
    }
)
```

### Ollama

```python
agent = Agent(
    name="Ollama Agent",
    model="ollama:llama3.2",
    llm_provider_config={
        "base_url": "http://localhost:11434"
    }
)
```

## 🛡️ Guardrails

Add safety measures to agent inputs and outputs:

```python
from igniteai_agent_sdk.guardrails import Guardrail

class ContentFilter(Guardrail):
    async def validate_input(self, input_text: str) -> bool:
        # Validate user input
        return not any(word in input_text.lower() for word in ["spam", "harmful"])

    async def validate_output(self, output_text: str) -> bool:
        # Validate agent output
        return len(output_text) < 1000

agent = Agent(
    name="Safe Agent",
    input_guardrails=[ContentFilter()],
    output_guardrails=[ContentFilter()]
)
```

## 📊 Telemetry & Usage Tracking

Monitor agent performance and API usage:

```python
from igniteai_agent_sdk.telemetry import Telemetry
from igniteai_agent_sdk.usage_tracker import UsageTracker

agent = Agent(name="Tracked Agent")

# Access telemetry data
telemetry = agent.telemetry
traces = telemetry.get_traces()

# Usage tracking
usage = agent.usage_tracker
total_tokens = usage.get_total_tokens()
total_cost = usage.get_total_cost()
```

## 🔄 Event System

Handle real-time events:

```python
from igniteai_agent_sdk.types import AgentStartEvent, AgentStopEvent, LLMResponseEvent

agent = Agent(name="Event Agent")

@agent.event_emitter.on(AgentStartEvent)
async def on_agent_start(event):
    print(f"Agent {event.agent_name} started")

@agent.event_emitter.on(LLMResponseEvent)
async def on_llm_response(event):
    print(f"LLM responded with: {event.content}")

response = await agent.run("Hello")
```

## 🌐 MCP Integration

Use Model Context Protocol servers:

```python
from igniteai_agent_sdk.agent import ReActAgent
from igniteai_agent_sdk.tools.mcp import MCPServer

agent = ReActAgent(
    name="MCP Agent",
    mcp_servers=[
        MCPServer(
            name="filesystem",
            command="npx",
            args=["-y", "@modelcontextprotocol/server-filesystem", "/path/to/directory"]
        ),
        MCPServer(
            name="weather",
            sse_url="http://localhost:8080/sse"
        )
    ]
)

response = await agent.run("List files in the current directory")
```

## 🌊 Streaming Support

The SDK provides comprehensive streaming support for real-time agent responses:

### Basic Streaming

```python
import asyncio
from igniteai_agent_sdk.agent import Agent

agent = Agent(name="StreamBot")

async def stream_example():
    async for chunk in agent.run_stream("Explain quantum computing"):
        if chunk.delta:
            print(chunk.delta, end="", flush=True)
        if chunk.is_final:
            break
```

### Streaming with Events

Listen to streaming events for advanced control:

```python
from igniteai_agent_sdk.types import (
    LLMStreamStartEvent,
    LLMStreamChunkEvent, 
    LLMStreamCompleteEvent
)

agent = Agent(name="EventStreamBot")

# Event handlers
async def on_stream_start(event):
    print(f"🚀 Starting stream for {event.model}")

async def on_chunk(event):
    print(f"📦 Chunk {event.chunk.chunk_index}: {len(event.chunk.delta or '')} chars")

async def on_complete(event):
    print(f"✅ Stream complete: {event.total_chunks} chunks")

# Register handlers
agent.event_emitter.on(LLMStreamStartEvent, on_stream_start)
agent.event_emitter.on(LLMStreamChunkEvent, on_chunk)
agent.event_emitter.on(LLMStreamCompleteEvent, on_complete)

# Stream response
async for chunk in agent.run_stream("Your question here"):
    if chunk.delta:
        print(chunk.delta, end="")
    if chunk.is_final:
        break
```

### Streaming with Tools

Streaming works seamlessly with tool calls:

```python
from igniteai_agent_sdk.agent import ReActAgent
from igniteai_agent_sdk.tools import ToolKit

def calculate(expression: str) -> str:
    """Calculate mathematical expressions."""
    return f"Result: {eval(expression)}"

agent = ReActAgent(
    name="MathBot",
    tools=ToolKit(tools=[calculate])
)

async for chunk in agent.run_stream("What's 25 * 17 + 100?"):
    if chunk.delta:
        print(chunk.delta, end="", flush=True)
    if chunk.tool_calls:
        print(f"\n[Executing {len(chunk.tool_calls)} tools...]")
    if chunk.is_final:
        break
```

### Stream Response Types

The streaming API provides several key types:

- **StreamChunk**: Individual response chunks with delta content
- **LLMStreamStartEvent**: Emitted when streaming begins  
- **LLMStreamChunkEvent**: Emitted for each chunk
- **LLMStreamCompleteEvent**: Emitted when streaming finishes

## 📖 Advanced Examples

### Multi-Agent Collaboration

```python
# Create specialized agents for different domains
code_agent = Agent(
    name="Code Expert",
    introduction="You are a programming expert",
    task="Write and review code",
    tools=[code_analyzer, syntax_checker]
)

docs_agent = Agent(
    name="Documentation Expert",
    introduction="You are a technical writer",
    task="Create clear documentation",
    tools=[doc_generator, style_checker]
)

# Supervisor agent coordinates the specialists
supervisor = ReActAgent(
    name="Project Manager",
    introduction="You coordinate development tasks",
    task="Manage code and documentation tasks",
    agents=[code_agent, docs_agent],
    instructions=[
        "For coding tasks, delegate to Code Expert",
        "For documentation tasks, delegate to Documentation Expert",
        "Ensure code and docs are consistent"
    ]
)
```

### Custom Tool Integration

```python
from igniteai_agent_sdk.tools import Tool
import requests

class WeatherTool(Tool):
    def __init__(self, api_key: str):
        self.api_key = api_key
        super().__init__()

    def get_weather(self, city: str, country: str = "US") -> str:
        """Get current weather for a location.

        Args:
            city: City name
            country: Country code (default: US)
        """
        url = f"http://api.openweathermap.org/data/2.5/weather"
        params = {
            "q": f"{city},{country}",
            "appid": self.api_key,
            "units": "imperial"
        }

        response = requests.get(url, params=params)
        data = response.json()

        return f"Weather in {city}: {data['weather'][0]['description']}, {data['main']['temp']}°F"

weather_tool = WeatherTool(api_key="your-weather-api-key")
agent = Agent(
    name="Weather Agent",
    tools=[weather_tool]
)
```

### Human-in-the-Loop Workflows

```python
from igniteai_agent_sdk.workflow import Workflow
from igniteai_agent_sdk.types import HumanApprovalRequestEvent

workflow = Workflow(name="Approval Workflow")

@workflow.task(DataProcessedEvent)
async def request_approval(event):
    # Request human approval before proceeding
    return HumanApprovalRequestEvent(
        workflow_id=workflow.id,
        task_id="approval_task",
        prompt="Approve this data processing result?",
        data=event.result
    )

@workflow.task(HumanApprovalResponseEvent)
async def handle_approval(event):
    if event.approved:
        return FinalProcessingEvent(data=event.data)
    else:
        return WorkflowStopEvent(result="Cancelled by user")
```

## 🧪 Testing

Run the test suite:

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest tests/

# Run with coverage
pytest tests/ --cov=igniteai_agent_sdk
```

## 📁 Project Structure

```
igniteai-agent-sdk/
├── src/igniteai_agent_sdk/
│   ├── agent/           # Agent implementations
│   ├── llm/             # LLM provider integrations
│   ├── memory/          # Memory systems
│   ├── tools/           # Tool framework
│   ├── workflow/        # Workflow engine
│   ├── prompts/         # Prompt templates
│   ├── config.py        # Configuration management
│   ├── types.py         # Type definitions
│   ├── telemetry.py     # Monitoring and tracing
│   └── usage_tracker.py # API usage tracking
├── examples/            # Example implementations
├── tests/              # Test suite
└── docs/               # Documentation
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- [Documentation](docs/)
- [Examples](examples/)
- [API Reference](docs/api-reference.md)
- [How-to Guides](docs/)

## 💬 Support

- GitHub Issues: Report bugs and request features
- Discussions: Ask questions and share ideas
- Documentation: Comprehensive guides and API reference

---

Built with ❤️ by the IgniteAI team
