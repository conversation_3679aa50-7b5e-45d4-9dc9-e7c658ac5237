import asyncio

import pytest

from igniteai_agent_sdk._agent_context import Context, deep_merge_dicts


class TestDeepMergeDicts:
    def test_basic_merge(self):
        """Test basic dictionary merge functionality."""
        base = {"a": 1, "b": 2}
        update = {"b": 3, "c": 4}
        result = deep_merge_dicts(base, update)

        assert result == {"a": 1, "b": 3, "c": 4}

        assert base == {"a": 1, "b": 2}
        assert update == {"b": 3, "c": 4}

    def test_nested_merge(self):
        """Test merge with nested dictionaries."""
        base = {"a": 1, "nested": {"x": 10, "y": 20}}
        update = {"b": 2, "nested": {"y": 30, "z": 40}}
        result = deep_merge_dicts(base, update)

        assert result == {"a": 1, "b": 2, "nested": {"x": 10, "y": 30, "z": 40}}


class TestContext:
    def test_initialization(self):
        """Test Context initialization."""
        context = Context()
        assert context.context == {}
        assert context.history == []

        context_with_data = Context(context={"key": "value"})
        assert context_with_data.context == {"key": "value"}

    def test_synchronous_update(self):
        """Test synchronous context update."""
        context = Context()
        context.update({"key": "value"}, author="test")

        assert context.context == {"key": "value"}
        assert len(context.history) == 1
        assert context.history[0]["author"] == "test"
        assert context.history[0]["changes"] == {"key": "value"}

    def test_synchronous_get(self):
        """Test synchronous context retrieval."""
        context = Context(context={"key": "value"})
        result = context.get()

        assert result == {"key": "value"}

        result["new_key"] = "new_value"
        assert "new_key" not in context.context

    def test_synchronous_reset(self):
        """Test synchronous context reset."""
        context = Context(context={"key": "value"})
        context.reset()

        assert context.context == {}

    def test_rollback(self):
        """Test rollback functionality."""
        context = Context()
        context.update({"step1": "value1"})
        context.update({"step2": "value2"})
        context.update({"step3": "value3"})

        assert context.context == {"step1": "value1", "step2": "value2", "step3": "value3"}

        context.rollback(1)
        assert context.context == {"step1": "value1", "step2": "value2"}
        assert len(context.history) == 2

        context.rollback(2)
        assert context.context == {}
        assert len(context.history) == 0

    @pytest.mark.asyncio
    async def test_asynchronous_update(self):
        """Test asynchronous context update."""
        context = Context()
        await context.aupdate({"key": "value"}, author="test")

        assert context.context == {"key": "value"}
        assert len(context.history) == 1

    @pytest.mark.asyncio
    async def test_asynchronous_get(self):
        """Test asynchronous context retrieval."""
        context = Context(context={"key": "value"})
        result = await context.aget()

        assert result == {"key": "value"}

    @pytest.mark.asyncio
    async def test_asynchronous_reset(self):
        """Test asynchronous context reset."""
        context = Context(context={"key": "value"})
        await context.areset()

        assert context.context == {}

    @pytest.mark.asyncio
    async def test_asynchronous_rollback(self):
        """Test asynchronous rollback."""
        context = Context()
        await context.aupdate({"step1": "value1"})
        await context.aupdate({"step2": "value2"})

        await context.arollback(1)
        assert context.context == {"step1": "value1"}

    @pytest.mark.asyncio
    async def test_concurrent_updates(self):
        """Test concurrent updates to the context."""
        context = Context()

        async def update_task(key, value, delay):
            await asyncio.sleep(delay)
            await context.aupdate({key: value})

        await asyncio.gather(update_task("key1", "value1", 0.1), update_task("key2", "value2", 0.05))

        assert context.context == {"key1": "value1", "key2": "value2"}
        assert len(context.history) == 2
