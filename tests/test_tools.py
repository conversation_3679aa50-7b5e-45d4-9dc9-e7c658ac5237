import pytest
from pydantic import BaseModel

from igniteai_agent_sdk._agent_context import Context
from igniteai_agent_sdk.tools.base_tool import Tool, convert_to_tool_spec, extract_param_descriptions
from igniteai_agent_sdk.types import Result


class TestToolCreation:
    def test_tool_creation_from_function(self, sample_tool_function):
        """Test creating a Tool from a callable."""
        tool = Tool.infer_from_callable(sample_tool_function)

        assert tool.name == "echo"
        assert "Echo back the input message" in tool.description
        assert tool._function == sample_tool_function
        assert "message" in tool.parameters["properties"]
        assert tool.requires_context is False

    def test_tool_with_context(self):
        """Test creating a Tool that requires Context."""

        def func_with_context(text: str, context: Context) -> str:
            """A function that requires context.

            Args:
                text: Input text
                context: Agent context

            Returns:
                A string response

            """
            return text

        tool = Tool.infer_from_callable(func_with_context)

        assert tool.requires_context is True
        assert "text" in tool.parameters["properties"]
        assert "context" not in tool.parameters["properties"]

    def test_tool_with_custom_name(self, sample_tool_function):
        """Test creating a Tool with a custom name."""
        tool = Tool.infer_from_callable(sample_tool_function, tool_name="custom_echo")

        assert tool.name == "custom_echo"

    def test_tool_with_custom_description(self, sample_tool_function):
        """Test creating a Tool with a custom description."""
        tool = Tool.infer_from_callable(
            sample_tool_function,
            description="""Custom description of the echo tool

            Args:
                message: The input message to echo back
            """,
            ignore_validations=True,
        )

        assert "Custom description of the echo tool" in tool.description

    def test_tool_call(self, sample_tool_function):
        """Test calling a Tool directly."""
        tool = Tool.infer_from_callable(sample_tool_function)

        result = tool("hello")

        assert result == "hello"

    def test_extract_param_descriptions(self):
        """Test extraction of parameter descriptions from docstring."""

        def test_func(a: int, b: str, c: float = 0.0) -> None:
            """Test function with docstring.

            Args:
                a: First parameter
                b: Second parameter
                c: Third parameter with default

            """
            pass

        descriptions = extract_param_descriptions(test_func)

        assert descriptions["a"] == "First parameter"
        assert descriptions["b"] == "Second parameter"
        assert descriptions["c"] == "Third parameter with default"

    def test_convert_to_tool_spec(self):
        """Test conversion of a function to a tool specification."""

        def test_func(a: int, b: str = "default") -> None:
            """Test function."""
            pass

        class MockParamModel(BaseModel):
            a: int
            b: str = "default"

        tool_spec = convert_to_tool_spec(test_func, MockParamModel)

        assert tool_spec["name"] == "test_func"
        assert tool_spec["parameters"]["type"] == "object"
        assert "a" in tool_spec["parameters"]["properties"]
        assert "b" in tool_spec["parameters"]["properties"]
        assert tool_spec["parameters"]["properties"]["a"]["type"] == "integer"
        assert tool_spec["parameters"]["properties"]["b"]["type"] == "string"
        assert tool_spec["parameters"]["properties"]["b"]["default"] == "default"
        assert "a" in tool_spec["parameters"]["required"]
        assert "b" not in tool_spec["parameters"]["required"]


class TestToolExecution:
    def test_wrap_helper_function(self, sample_tool_function):
        """Test the wrap helper function."""
        from igniteai_agent_sdk.tools.base_tool import wrap

        tool = wrap(sample_tool_function)

        assert isinstance(tool, Tool)
        assert tool.name == "echo"

    def test_tool_result_handling(self):
        """Test handling different return types in a tool."""

        def string_func(text: str) -> str:
            """Return a string.

            Args:
                text: Input text

            """
            return text

        tool = Tool.infer_from_callable(string_func)
        result = tool("test")

        assert result == "test"

        def result_func(text: str) -> Result:
            """Return a Result object.

            Args:
                text: Input text

            """
            return Result(value=text)

        tool = Tool.infer_from_callable(result_func)
        result = tool("test")

        assert isinstance(result, Result)
        assert result.value == "test"

    @pytest.mark.asyncio
    async def test_async_tool_function(self):
        """Test a Tool with an async function."""

        async def async_echo(text: str) -> str:
            """Echo back the text asynchronously.

            Args:
                text: Input text

            """
            return text

        tool = Tool.infer_from_callable(async_echo)

        from igniteai_agent_sdk.llm.types import ChatCompletionMessageToolCall, Function
        from igniteai_agent_sdk.tools.toolkit import ToolKit

        toolkit = ToolKit([tool])

        function = Function(name="async_echo", arguments='{"text": "hello async"}')
        tool_call = ChatCompletionMessageToolCall(id="test-call-id", type="function", function=function)

        tool_response = await toolkit.execute_tools([tool_call])

        assert len(tool_response.results) == 1
        assert tool_response.results[0].value == "hello async"
