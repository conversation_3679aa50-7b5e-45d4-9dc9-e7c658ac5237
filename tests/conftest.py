import asyncio
from unittest.mock import patch

import pytest

from igniteai_agent_sdk._agent_context import Context
from igniteai_agent_sdk.agent.base_agent import Agent


@pytest.fixture
def context():
    """Fixture to provide a clean Context instance."""
    return Context()


@pytest.fixture
def mock_complete():
    """Mock for igniteai_agent_sdk.llm.LLM.complete function."""
    with patch("igniteai_agent_sdk.llm.LLM.complete") as mock:
        yield mock


@pytest.fixture
def agent():
    """Basic agent fixture with default parameters."""
    return Agent(name="TestAgent")


@pytest.fixture
def event_loop():
    """Create an asyncio event loop for tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def sample_tool_function():
    """Sample tool function for testing."""

    def echo(message: str) -> str:
        """Echo back the input message.

        Args:
            message: The message to echo back

        Returns:
            The same message

        """
        return message

    return echo
