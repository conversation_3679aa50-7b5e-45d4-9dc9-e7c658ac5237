"""Unit tests for LangChain tools integration."""

from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from pydantic import BaseModel, Field

from igniteai_agent_sdk.tools.base_tool import BaseTool, wrap


# Create a model to be used as the return value of get_input_schema
class ToolInputModel(BaseModel):
    tool_input: str = Field(description="Input for the tool")


class TestLangChainToolIntegration:
    def test_langchain_tool_detection(self):
        """Test that LangChain tools are correctly identified."""
        # Create a mock LangChain tool
        mock_langchain_tool = MagicMock()
        mock_langchain_tool.name = "test_lc_tool"
        mock_langchain_tool.description = "A test LangChain tool"
        mock_langchain_tool.__call__ = lambda x: f"Result for {x}"

        # Make it match the BaseTool protocol
        type(mock_langchain_tool).__annotations__ = {"name": str, "description": str}

        # Verify it's seen as a BaseTool
        assert isinstance(mock_langchain_tool, BaseTool)

    def test_wrap_langchain_tool(self):
        """Test wrapping a LangChain tool."""
        # Create a mock LangChain tool
        mock_langchain_tool = MagicMock()
        mock_langchain_tool.name = "search"
        mock_langchain_tool.description = "Search the web"
        mock_langchain_tool._run = lambda tool_input: f"Search results for {tool_input}"
        # Add the new methods required by the updated from_langchain_tool
        mock_langchain_tool.get_input_schema = MagicMock(return_value=ToolInputModel)
        mock_langchain_tool.get_input_jsonschema = MagicMock(
            return_value={"type": "object", "properties": {"tool_input": {"type": "string"}}, "required": ["tool_input"]}
        )

        # Make it match the BaseTool protocol
        type(mock_langchain_tool).__annotations__ = {"name": str, "description": str}

        # Wrap the tool
        with patch("igniteai_agent_sdk.tools.base_tool.logger") as mock_logger:
            tool = wrap(mock_langchain_tool)
            # Verify logger was called with the expected message
            mock_logger.debug.assert_called_once_with(f"Converting LangChain tool '{mock_langchain_tool.name}' to ignite-ai tool")

        # Verify the wrapped tool
        assert tool.name == "search"
        assert tool.description == "Search the web"
        assert tool._source == "langchain"
        assert "_metadata" in dir(tool)
        assert tool._metadata.get("original_tool") == mock_langchain_tool.__class__.__name__

    def test_langchain_tool_schema(self):
        """Test that the schema for LangChain tools is correctly generated."""
        # Create a mock LangChain tool
        mock_langchain_tool = MagicMock()
        mock_langchain_tool.name = "translate"
        mock_langchain_tool.description = "Translate text"
        mock_langchain_tool._run = lambda tool_input: f"Translated: {tool_input}"

        # Set up the schema and model
        input_jsonschema = {
            "type": "object",
            "properties": {"tool_input": {"type": "string", "description": "The text to translate"}},
            "required": ["tool_input"],
        }
        mock_langchain_tool.get_input_jsonschema = MagicMock(return_value=input_jsonschema)
        mock_langchain_tool.get_input_schema = MagicMock(return_value=ToolInputModel)

        # Make it match the BaseTool protocol
        type(mock_langchain_tool).__annotations__ = {"name": str, "description": str}

        # Wrap the tool
        tool = wrap(mock_langchain_tool)

        # Check the parameter schema
        assert "tool_input" in tool.parameters["properties"]
        assert tool.parameters["required"] == ["tool_input"]

    @pytest.mark.asyncio
    async def test_langchain_tool_execution(self):
        """Test executing a wrapped LangChain tool."""
        # Create a mock LangChain tool
        mock_langchain_tool = MagicMock()
        mock_langchain_tool.name = "calculator"
        mock_langchain_tool.description = "Perform calculations"
        mock_langchain_tool._run = lambda tool_input: f"Result: {eval(tool_input)}"

        async def tool(tool_input: str) -> str:
            return f"Result: {eval(tool_input)}"

        mock_langchain_tool._arun = tool

        # Set up the schema and model
        mock_langchain_tool.get_input_jsonschema = MagicMock(
            return_value={"type": "object", "properties": {"tool_input": {"type": "string"}}, "required": ["tool_input"]}
        )
        mock_langchain_tool.get_input_schema = MagicMock(return_value=ToolInputModel)

        # Make it match the BaseTool protocol
        type(mock_langchain_tool).__annotations__ = {"name": str, "description": str}

        # Wrap the tool
        tool = wrap(mock_langchain_tool)

        # Execute the wrapped tool
        result = await tool._function(tool_input="2 + 2")

        # Verify the result
        assert result == "Result: 4"

    @pytest.mark.asyncio
    async def test_langchain_tool_error_handling(self):
        """Test error handling in wrapped LangChain tools."""
        # Create a mock LangChain tool that raises an exception
        mock_langchain_tool = MagicMock()
        mock_langchain_tool.name = "error_tool"
        mock_langchain_tool.description = "A tool that raises an error"
        mock_langchain_tool._run = MagicMock(side_effect=ValueError("Test error"))
        mock_langchain_tool._arun = AsyncMock(side_effect=ValueError("Test error"))

        # Set up the schema and model
        mock_langchain_tool.get_input_jsonschema = MagicMock(
            return_value={"type": "object", "properties": {"tool_input": {"type": "string"}}, "required": ["tool_input"]}
        )
        mock_langchain_tool.get_input_schema = MagicMock(return_value=ToolInputModel)

        # Make it match the BaseTool protocol
        type(mock_langchain_tool).__annotations__ = {"name": str, "description": str}

        # Wrap the tool
        with patch("igniteai_agent_sdk.tools.base_tool.logger") as mock_logger:
            tool = wrap(mock_langchain_tool)

            # Execute the wrapped tool and check error handling
            result = await tool._function(tool_input="test")

            # Verify the error is logged and returned properly
            mock_logger.error.assert_called_once()
            assert "Error: Test error" in result

    def test_error_on_invalid_input(self):
        """Test that an appropriate error is raised for invalid inputs."""
        # Try to wrap something that's not a tool
        with pytest.raises(TypeError):
            wrap(42)  # Not a callable or BaseTool

    def test_callable_wrapping(self):
        """Test that regular callables are still wrapped correctly."""

        def example_func(x: int, y: int) -> int:
            """Add two numbers.

            Args:
                x: First number
                y: Second number

            Returns:
                Sum of x and y

            """
            return x + y

        # Wrap the callable
        tool = wrap(example_func)

        # Verify it's wrapped as a normal function, not as a LangChain tool
        assert tool.name == "example_func"
        assert tool._source == "custom"
        assert "x" in tool.parameters["properties"]
        assert "y" in tool.parameters["properties"]
