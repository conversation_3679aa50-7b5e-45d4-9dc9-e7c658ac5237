from unittest.mock import AsyncMock, MagicMock

import pytest

from igniteai_agent_sdk._event_emitter import EventEmitter
from igniteai_agent_sdk.types import Event


class TestEvent(Event):
    name: str = "test_event"
    data: str = "value"


class TestEventEmitter:
    def test_initialization(self):
        """Test EventEmitter initialization."""
        emitter = EventEmitter()
        assert hasattr(emitter, "_listeners")
        assert isinstance(emitter._listeners, dict)

    def test_on_handler_registration(self):
        """Test registering event handlers."""
        emitter = EventEmitter()
        handler = MagicMock()

        emitter.on(TestEvent, handler)

        assert "TestEvent" in emitter._listeners
        assert handler in emitter._listeners["TestEvent"]

    def test_off_handler_removal(self):
        """Test removing event handlers."""
        emitter = EventEmitter()
        handler1 = MagicMock()
        handler2 = MagicMock()

        emitter.on(TestEvent, handler1)
        emitter.on(TestEvent, handler2)

        assert len(emitter._listeners["TestEvent"]) == 2

        emitter.off(TestEvent, handler1)

        assert len(emitter._listeners["TestEvent"]) == 1
        assert handler2 in emitter._listeners["TestEvent"]
        assert handler1 not in emitter._listeners["TestEvent"]

    def test_off_nonexistent_handler(self):
        """Test removing a handler that doesn't exist (should not raise)."""
        emitter = EventEmitter()
        handler = MagicMock()

        emitter.off(TestEvent, handler)

    @pytest.mark.asyncio
    async def test_emit_sync_handlers(self):
        """Test emitting events to synchronous handlers."""
        emitter = EventEmitter()
        handler = MagicMock()

        emitter.on(TestEvent, handler)
        await emitter.emit(TestEvent())

    @pytest.mark.asyncio
    async def test_emit_async_handlers(self):
        """Test emitting events to asynchronous handlers."""
        emitter = EventEmitter()
        handler = AsyncMock()

        emitter.on(TestEvent, handler)
        await emitter.emit(TestEvent())

    @pytest.mark.asyncio
    async def test_emit_multiple_handlers(self):
        """Test emitting events to multiple handlers."""
        emitter = EventEmitter()
        handler1 = MagicMock()
        handler2 = AsyncMock()

        emitter.on(TestEvent, handler1)
        emitter.on(TestEvent, handler2)

        await emitter.emit(TestEvent())

    @pytest.mark.asyncio
    async def test_emit_no_handlers(self):
        """Test emitting events with no registered handlers."""
        emitter = EventEmitter()

        await emitter.emit(TestEvent())
