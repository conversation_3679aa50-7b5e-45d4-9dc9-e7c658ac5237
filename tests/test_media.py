"""Tests for the Media class and related functionality."""

import base64
import tempfile
from pathlib import Path
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import aiohttp
import pytest

from igniteai_agent_sdk.media import Audio, Image, Media, MediaType


class TestMediaValidation:
    """Test media validation logic."""

    def test_media_requires_source(self):
        """Test that Media requires either url or file_path."""
        with pytest.raises(ValueError, match="Either 'url' or 'file_path' must be provided"):
            Media(media_type=MediaType.IMAGE)

    def test_media_rejects_both_sources(self):
        """Test that Media rejects both url and file_path."""
        with pytest.raises(ValueError, match="Only one of 'url' or 'file_path' should be provided"):
            Media(media_type=MediaType.IMAGE, url="https://example.com/image.jpg", file_path="image.jpg")

    def test_invalid_url(self):
        """Test validation of invalid URLs."""
        with pytest.raises(ValueError, match="Invalid URL format"):
            Media(media_type=MediaType.IMAGE, url="not-a-url")

    def test_file_path_conversion(self):
        """Test that string file paths are converted to Path objects."""
        media = Media(media_type=MediaType.IMAGE, file_path="test.jpg")
        assert isinstance(media.file_path, Path)
        assert str(media.file_path) == "test.jpg"


class TestMediaMimeTypeDetection:
    """Test MIME type detection and validation."""

    def test_image_mime_type_from_extension(self):
        """Test MIME type detection for images."""
        media = Media(media_type=MediaType.IMAGE, file_path="test.jpg")

        # Test various image extensions
        test_cases = [
            (".jpg", "image/jpeg"),
            (".jpeg", "image/jpeg"),
            (".png", "image/png"),
            (".gif", "image/gif"),
            (".webp", "image/webp"),
        ]

        for ext, expected_mime in test_cases:
            result = media._get_mime_type_from_extension(ext)
            assert result == expected_mime

    def test_audio_mime_type_from_extension(self):
        """Test MIME type detection for audio."""
        media = Media(media_type=MediaType.AUDIO, file_path="test.mp3")

        # Test various audio extensions
        test_cases = [(".mp3", "audio/mpeg"), (".wav", "audio/wav")]

        for ext, expected_mime in test_cases:
            result = media._get_mime_type_from_extension(ext)
            assert result == expected_mime

    def test_mime_type_validation_success(self):
        """Test successful MIME type validation."""
        image_media = Media(media_type=MediaType.IMAGE, file_path="test.jpg")
        audio_media = Media(media_type=MediaType.AUDIO, file_path="test.mp3")

        # These should not raise
        image_media._validate_mime_type("image/jpeg")
        audio_media._validate_mime_type("audio/mpeg")

    def test_mime_type_validation_failure(self):
        """Test MIME type validation failures."""
        image_media = Media(media_type=MediaType.IMAGE, file_path="test.jpg")
        audio_media = Media(media_type=MediaType.AUDIO, file_path="test.mp3")

        with pytest.raises(ValueError, match="Expected image MIME type"):
            image_media._validate_mime_type("audio/mpeg")

        with pytest.raises(ValueError, match="Expected audio MIME type"):
            audio_media._validate_mime_type("image/jpeg")


class TestMediaFileLoading:
    """Test loading media from files."""

    @pytest.fixture
    def temp_image_file(self):
        """Create a temporary image file for testing."""
        # Create a simple 1x1 PNG image
        png_data = base64.b64decode(
            "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
        )

        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as f:
            f.write(png_data)
            f.flush()
            yield Path(f.name)

        # Cleanup
        Path(f.name).unlink(missing_ok=True)

    @pytest.fixture
    def temp_audio_file(self):
        """Create a temporary audio file for testing."""
        # Create a minimal WAV file header
        wav_data = b"RIFF\x24\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x44\xac\x00\x00\x88\x58\x01\x00\x02\x00\x10\x00data\x00\x00\x00\x00"

        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as f:
            f.write(wav_data)
            f.flush()
            yield Path(f.name)

        # Cleanup
        Path(f.name).unlink(missing_ok=True)

    @pytest.mark.asyncio
    async def test_load_image_from_file(self, temp_image_file):
        """Test loading an image from file."""
        media = Media(media_type=MediaType.IMAGE, file_path=temp_image_file)
        await media.load()

        assert media.content is not None
        assert media.mime_type == "image/png"
        assert media.size_bytes > 0
        assert media.size_bytes == temp_image_file.stat().st_size

    @pytest.mark.asyncio
    async def test_load_audio_from_file(self, temp_audio_file):
        """Test loading audio from file."""
        media = Media(media_type=MediaType.AUDIO, file_path=temp_audio_file)
        await media.load()

        assert media.content is not None
        assert media.mime_type == "audio/wav"
        assert media.size_bytes > 0

    @pytest.mark.asyncio
    async def test_load_nonexistent_file(self):
        """Test loading from nonexistent file."""
        media = Media(media_type=MediaType.IMAGE, file_path="nonexistent.jpg")

        with pytest.raises(FileNotFoundError):
            await media.load()

    @pytest.mark.asyncio
    async def test_file_too_large(self, temp_image_file):
        """Test handling of files that are too large."""
        media = Media(media_type=MediaType.IMAGE, file_path=temp_image_file, max_file_size=1)  # 1 byte limit

        with pytest.raises(ValueError, match="File too large"):
            await media.load()

    @pytest.mark.asyncio
    async def test_load_already_loaded(self, temp_image_file):
        """Test that loading an already loaded media returns self."""
        media = Media(media_type=MediaType.IMAGE, file_path=temp_image_file)
        await media.load()

        original_data = media.content
        result = await media.load()  # Load again

        assert result is media
        assert media.content == original_data


class TestMediaURLLoading:
    """Test loading media from URLs."""

    @pytest.fixture
    def mock_aiohttp_session(self):
        """Mock aiohttp session for testing."""
        mock_context = MagicMock()
        mock_session = MagicMock()
        mock_response = MagicMock()

        # Create AsyncMock for async methods, not for the context manager itself
        mock_context.__aenter__ = AsyncMock(return_value=mock_session)
        mock_context.__aexit__ = AsyncMock(return_value=None)

        mock_get_response = MagicMock()
        mock_get_response.__aenter__ = AsyncMock(return_value=mock_response)
        mock_get_response.__aexit__ = AsyncMock(return_value=None)

        mock_session.get = MagicMock(return_value=mock_get_response)
        mock_response.raise_for_status = MagicMock()
        mock_response.headers = {"content-type": "image/jpeg", "content-length": "1000"}
        mock_response.read = AsyncMock(return_value=b"fake_image_data")

        with patch("aiohttp.ClientSession", return_value=mock_context):
            yield mock_session, mock_response

    @pytest.mark.asyncio
    async def test_load_image_from_url(self, mock_aiohttp_session):
        """Test loading an image from URL."""
        mock_session, mock_response = mock_aiohttp_session

        media = Media(media_type=MediaType.IMAGE, url="https://example.com/image.jpg")
        await media.load()

        assert media.content == base64.b64encode(b"fake_image_data").decode("utf-8")
        assert media.mime_type == "image/jpeg"
        assert media.size_bytes == len(b"fake_image_data")

    @pytest.mark.asyncio
    async def test_url_content_too_large_header(self, mock_aiohttp_session):
        """Test handling of content that's too large (from headers)."""
        mock_session, mock_response = mock_aiohttp_session
        mock_response.headers = {"content-length": "999999999"}  # Very large

        media = Media(media_type=MediaType.IMAGE, url="https://example.com/image.jpg", max_file_size=1000)

        with pytest.raises(ValueError, match="Content too large"):
            await media.load()

    @pytest.mark.asyncio
    async def test_url_content_too_large_actual(self, mock_aiohttp_session):
        """Test handling of content that's too large (actual content)."""
        mock_session, mock_response = mock_aiohttp_session
        mock_response.headers = {}  # No content-length header
        mock_response.read = AsyncMock(return_value=b"x" * 999999)  # Very large content

        media = Media(media_type=MediaType.IMAGE, url="https://example.com/image.jpg", max_file_size=1000)

        with pytest.raises(ValueError, match="Content too large"):
            await media.load()

    @pytest.mark.asyncio
    async def test_url_http_error(self):
        """Test handling of HTTP errors."""
        # Create properly structured mocks for context managers
        mock_context = MagicMock()
        mock_session = MagicMock()
        mock_response = MagicMock()

        mock_context.__aenter__ = AsyncMock(return_value=mock_session)
        mock_context.__aexit__ = AsyncMock(return_value=None)

        mock_get_response = MagicMock()
        mock_get_response.__aenter__ = AsyncMock(return_value=mock_response)
        mock_get_response.__aexit__ = AsyncMock(return_value=None)

        mock_session.get = MagicMock(return_value=mock_get_response)
        mock_response.raise_for_status.side_effect = aiohttp.ClientResponseError(request_info=MagicMock(), history=MagicMock())

        with patch("aiohttp.ClientSession", return_value=mock_context):
            media = Media(media_type=MediaType.IMAGE, url="https://example.com/image.jpg")

            with pytest.raises(aiohttp.ClientResponseError):
                await media.load()


class TestMediaUtilityMethods:
    """Test utility methods of the Media class."""

    @pytest.fixture
    def loaded_media(self):
        """Create a loaded media instance for testing."""
        media = Media(media_type=MediaType.IMAGE, file_path="test.jpg")
        media.content = "dGVzdCBkYXRh"  # "test data" in base64
        media.mime_type = "image/jpeg"
        media.size_bytes = 9
        return media

    def test_get_data_url(self, loaded_media):
        """Test generating data URL."""
        data_url = loaded_media.get_data_url()
        assert data_url == "data:image/jpeg;base64,dGVzdCBkYXRh"

    def test_get_data_url_not_loaded(self):
        """Test getting data URL from unloaded media."""
        media = Media(media_type=MediaType.IMAGE, file_path="test.jpg")

        with pytest.raises(ValueError, match="Media not loaded"):
            media.get_data_url()

    def test_save_to_file(self, loaded_media):
        """Test saving media to file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            save_path = Path(temp_dir) / "saved_image.jpg"
            loaded_media.save_to_file(save_path)

            assert save_path.exists()
            content = save_path.read_bytes()
            assert content == b"test data"

    def test_save_to_file_not_loaded(self):
        """Test saving unloaded media to file."""
        media = Media(media_type=MediaType.IMAGE, file_path="test.jpg")

        with pytest.raises(ValueError, match="Media not loaded"):
            media.save_to_file("output.jpg")

    def test_string_representation(self, loaded_media):
        """Test string representations."""
        str_repr = str(loaded_media)
        assert "Image" in str_repr or "image" in str_repr
        assert "loaded" in str_repr
        assert "9 bytes" in str_repr

        detailed_repr = repr(loaded_media)
        assert "Media(" in detailed_repr
        assert "media_type='image'" in detailed_repr
        assert "loaded=True" in detailed_repr


class TestConvenienceClasses:
    """Test convenience classes and functions."""

    def test_image_class(self):
        """Test Image convenience class."""
        image = Image(file_path="test.jpg")
        assert image.media_type == MediaType.IMAGE

    def test_audio_class(self):
        """Test Audio convenience class."""
        audio = Audio(file_path="test.mp3")
        assert audio.media_type == MediaType.AUDIO


class TestMediaClassMethods:
    """Test class methods for creating Media instances."""

    @pytest.mark.asyncio
    @patch.object(Media, "load")
    async def test_from_file_class_method(self, mock_load):
        """Test from_file class method."""
        mock_load.return_value = Media(media_type=MediaType.IMAGE, file_path="test.jpg")

        result = await Media.from_file("test.jpg", MediaType.IMAGE, max_file_size=1000)

        assert isinstance(result, Media)
        assert result.media_type == MediaType.IMAGE
        assert result.file_path == Path("test.jpg")
        assert result.max_file_size == 1000
        mock_load.assert_called_once()

    @pytest.mark.asyncio
    @patch.object(Media, "load")
    async def test_from_url_class_method(self, mock_load):
        """Test from_url class method."""
        mock_load.return_value = Media(media_type=MediaType.IMAGE, url="https://example.com/image.jpg")

        result = await Media.from_url("https://example.com/image.jpg", MediaType.IMAGE, timeout_seconds=60)

        assert isinstance(result, Media)
        assert result.media_type == MediaType.IMAGE
        assert result.url == "https://example.com/image.jpg"
        assert result.timeout_seconds == 60
        mock_load.assert_called_once()


class TestMediaConfiguration:
    """Test media configuration options."""

    def test_default_configuration(self):
        """Test default configuration values."""
        media = Media(media_type=MediaType.IMAGE, file_path="test.jpg")

        assert media.max_file_size == 50 * 1024 * 1024  # 50MB
        assert media.timeout_seconds == 30

    def test_custom_configuration(self):
        """Test custom configuration values."""
        media = Media(
            media_type=MediaType.IMAGE,
            file_path="test.jpg",
            max_file_size=1024,
            timeout_seconds=60,
        )

        assert media.max_file_size == 1024
        assert media.timeout_seconds == 60


if __name__ == "__main__":
    pytest.main([__file__])
