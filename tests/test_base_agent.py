from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from igniteai_agent_sdk._agent_context import Context
from igniteai_agent_sdk.agent.base_agent import Agent
from igniteai_agent_sdk.llm.types import ChatCompletionMessage as Message
from igniteai_agent_sdk.types import Response


class TestAgentInitialization:
    def test_basic_initialization(self):
        """Test basic agent initialization with minimal parameters."""
        agent = Agent(name="TestAgent")

        assert agent.name == "TestAgent"
        assert agent.introduction == "you are a helpful assistant"
        assert agent.task == "help the user with their query"
        assert agent.execute_tools is True

        assert agent.agent_id is not None
        assert isinstance(agent._context, Context)

    def test_tool_normalization(self):
        """Test tool normalization during initialization."""

        def sample_tool(text: str) -> str:
            """Sample tool.

            Args:
                text: Input text

            """
            return text

        agent = Agent(name="TestAgent", tools=[sample_tool])

        assert agent.tools_instance is not None
        assert "sample_tool" in agent.tools_instance._tools

    def test_initialize_with_agents(self):
        """Test initialization with sub-agents."""
        sub_agent = Agent(name="SubAgent")
        agent = Agent(name="MasterAgent", agents=[sub_agent])

        assert agent.has_agents is True
        assert agent.agents[0] is sub_agent


@pytest.mark.asyncio
class TestAgentExecution:
    @patch("igniteai_agent_sdk.llm.LLM.complete")
    async def test_run_basic(self, mock_complete):
        """Test the basic run method of the Agent."""
        # Setup the mock response
        mock_response = AsyncMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = Message(role="assistant", content="Test response")
        mock_complete.return_value = mock_response

        agent = Agent(name="TestAgent")
        response = await agent.run("Hello")

        # Check the response
        assert isinstance(response, Response)
        assert response.final_response is not None
        assert response.agent == "TestAgent"
