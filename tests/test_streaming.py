import asyncio

import pytest

from igniteai_agent_sdk.agent import Agent
from igniteai_agent_sdk.types import LLMStreamChunkEvent, LLMStreamCompleteEvent


@pytest.mark.asyncio
async def test_basic_streaming():
    """Test basic streaming functionality."""
    print("Testing Basic Streaming...")

    try:
        agent = Agent(name="TestBot", introduction="You are a test assistant", task="Provide test responses")

        print("Agent created successfully")

        # Test streaming
        print("Testing streaming response...")
        print("Response: ", end="", flush=True)

        chunk_count = 0
        total_content = ""

        async for chunk in agent.run_stream("Say hello world"):
            chunk_count += 1

            if chunk.delta:
                print(chunk.delta, end="", flush=True)
                total_content += chunk.delta

            if chunk.is_final:
                print("\nStreaming completed!")
                print(f"Total chunks: {chunk_count}")
                print(f"Content length: {len(total_content)}")
                break

        return True

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_streaming_events():
    """Test streaming with event handlers."""
    print("\n🧪 Testing Streaming Events...")

    try:
        agent = Agent(name="EventTestBot", introduction="You are a test assistant", task="Test event emission")

        events_received = {"start": 0, "chunk": 0, "complete": 0}

        async def on_chunk(event):
            events_received["chunk"] += 1

        async def on_complete(event):
            events_received["complete"] += 1
            print(f"\nStream completed with {event.total_chunks} chunks")

        # Register event handlers
        agent.event_emitter.on(LLMStreamChunkEvent, on_chunk)
        agent.event_emitter.on(LLMStreamCompleteEvent, on_complete)

        print("Response: ", end="", flush=True)

        async for chunk in agent.run_stream("Count to 5"):
            if chunk.delta:
                print(chunk.delta, end="", flush=True)
            if chunk.is_final:
                break

        print(f"\nEvents received: {events_received}")

        return events_received["chunk"] > 0 and events_received["complete"] > 0

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_streaming_vs_regular():
    """Compare streaming vs regular responses."""
    print("\nTesting Streaming vs Regular...")

    try:
        agent = Agent(name="CompareBot", introduction="You are a test assistant", task="Compare response modes")

        question = "What is 2+2?"

        # Regular response
        print("Regular response:")
        response = await agent.run(question)
        print(f"Result: {response.final_response}")

        # Streaming response
        print("\nStreaming response:")
        print("Result: ", end="", flush=True)

        streaming_content = ""
        async for chunk in agent.run_stream(question):
            if chunk.delta:
                print(chunk.delta, end="", flush=True)
                streaming_content += chunk.delta
            if chunk.is_final:
                break

        print(f"\nRegular length: {len(response.final_response)}")
        print(f"Streaming length: {len(streaming_content)}")

        return True

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


async def main():
    """Run all streaming tests."""
    print("IgniteAI Agent SDK - Streaming Tests\n")

    tests = [
        ("Basic Streaming", test_basic_streaming),
        ("Streaming Events", test_streaming_events),
        ("Streaming vs Regular", test_streaming_vs_regular),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
            status = "PASSED" if result else "❌ FAILED"
            print(f"{status}: {test_name}\n")
        except Exception as e:
            print(f"❌ FAILED: {test_name} - {str(e)}\n")
            results.append((test_name, False))

    # Summary
    print("Test Summary:")
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("All tests passed! Streaming implementation is working correctly.")
    else:
        print("Some tests failed. Check the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
