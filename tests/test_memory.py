import asyncio
import tempfile

import pytest

from igniteai_agent_sdk.memory.memory_manager import MemoryManager


@pytest.mark.asyncio
class TestMemoryManager:
    """Test suite for the MemoryManager class."""

    async def test_init_default_params(self):
        """Test initialization with default parameters."""
        manager = MemoryManager()
        assert manager.current_run_id is None
        assert manager.memory is not None
        assert manager.verbose is False

    async def test_set_run_id(self):
        """Test setting a run ID."""
        manager = MemoryManager()
        run_id = "test_run_123"
        manager.set_run_id(run_id)
        assert manager.current_run_id == run_id

    async def test_clear_run_id(self):
        """Test clearing a run ID."""
        manager = MemoryManager()
        run_id = "test_run_123"
        manager.set_run_id(run_id)
        manager.clear_run_id()
        assert manager.current_run_id is None

    async def test_remember_with_explicit_key(self):
        """Test storing memory with an explicit key."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            key = "test_key"
            content = "Test content"
            metadata = {"type": "test"}

            stored_key = await manager.remember(content, key, metadata)
            assert stored_key == key

            recalled_content = await manager.recall(key)
            assert recalled_content == content

    async def test_remember_with_generated_key(self):
        """Test storing memory with an auto-generated key."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            content = "Test content"

            stored_key = await manager.remember(content)
            assert stored_key.startswith("memory_")

            recalled_content = await manager.recall(stored_key)
            assert recalled_content == content

    async def test_remember_with_run_id(self):
        """Test storing memory with a run ID."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            content = "Test content"
            run_id = "test_run_123"

            await manager.remember(content, run_id=run_id)

            memories = await manager.recall_by_run(run_id)
            assert len(memories) == 1
            assert memories[0]["value"] == content

    async def test_recall_nonexistent_key(self):
        """Test recalling a non-existent key."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            result = await manager.recall("nonexistent_key")
            assert result is None

    async def test_recall_by_query(self):
        """Test searching memories by query."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")

            await manager.remember("The capital of France is Paris", "geography_1")
            await manager.remember("The capital of Italy is Rome", "geography_2")
            await manager.remember("Python is a programming language", "programming_1")

            results = await manager.recall_by_query("capital")

            if isinstance(results, bool):
                assert results is True
            else:
                assert len(results) == 2
                assert all("capital" in memory["value"].lower() for memory in results)

            results = await manager.recall_by_query("python")

            if isinstance(results, bool):
                assert results is True
            else:
                assert len(results) == 1
                assert "python" in results[0]["value"].lower()

    async def test_recall_by_run(self):
        """Test retrieving memories by run ID."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            run_id_1 = "run_1"
            run_id_2 = "run_2"

            await manager.remember("Memory from run 1, item 1", run_id=run_id_1)
            await manager.remember("Memory from run 1, item 2", run_id=run_id_1)
            await manager.remember("Memory from run 2", run_id=run_id_2)

            run_1_memories = await manager.recall_by_run(run_id_1)
            assert len(run_1_memories) == 2
            assert all("run 1" in memory["value"] for memory in run_1_memories)

            run_2_memories = await manager.recall_by_run(run_id_2)
            assert len(run_2_memories) == 1
            assert "run 2" in run_2_memories[0]["value"]

    async def test_recall_by_current_run(self):
        """Test retrieving memories using the current run ID."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            run_id = "current_run"
            manager.set_run_id(run_id)

            await manager.remember("Memory from current run")

            memories = await manager.recall_by_run()
            assert len(memories) == 1
            assert memories[0]["value"] == "Memory from current run"

    async def test_recall_recent(self):
        """Test retrieving recent memories."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")

            await manager.remember("First memory")
            await asyncio.sleep(0.01)
            await manager.remember("Second memory")
            await asyncio.sleep(0.01)
            await manager.remember("Third memory")

            recent_memories = await manager.recall_recent(limit=2)
            assert len(recent_memories) == 2

            assert recent_memories[0]["value"] == "Third memory"
            assert recent_memories[1]["value"] == "Second memory"

    async def test_forget_memory(self):
        """Test deleting a specific memory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            key = "to_delete"
            await manager.remember("Delete me", key)

            assert await manager.recall(key) == "Delete me"

            result = await manager.forget(key)
            assert result is True

            assert await manager.recall(key) is None

    async def test_forget_run(self):
        """Test deleting all memories for a run."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            run_id = "run_to_delete"

            await manager.remember("Memory 1 for run", run_id=run_id)
            await manager.remember("Memory 2 for run", run_id=run_id)
            await manager.remember("Memory for other run", run_id="other_run")

            run_memories = await manager.recall_by_run(run_id)
            assert len(run_memories) == 2

            count = await manager.forget_run(run_id)
            assert count == 2

            run_memories = await manager.recall_by_run(run_id)
            assert len(run_memories) == 0

            other_memories = await manager.recall_by_run("other_run")
            assert len(other_memories) == 1

    async def test_get_run_ids(self):
        """Test retrieving all run IDs."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")

            await manager.remember("Memory for run 1", run_id="run_1")
            await manager.remember("Memory for run 2", run_id="run_2")
            await manager.remember("Another memory for run 1", run_id="run_1")

            run_ids = await manager.get_run_ids()
            assert len(run_ids) == 2
            assert "run_1" in run_ids
            assert "run_2" in run_ids

    async def test_update_memory(self):
        """Test updating an existing memory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")
            key = "update_test"

            await manager.remember("Initial content", key, {"importance": "low"})

            result = await manager.update_memory(key, "Updated content", {"importance": "high"})
            assert result is True

            entry = await manager.memory.get(key)
            assert entry["value"] == "Updated content"
            assert entry["metadata"]["importance"] == "high"

    async def test_clear_all(self):
        """Test clearing all memories."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MemoryManager(memory_type="file", directory=temp_dir, filename="test_memory.json")

            await manager.remember("Memory 1")
            await manager.remember("Memory 2")

            await manager.clear_all()

            all_runs = await manager.get_run_ids()
            assert len(all_runs) == 0
