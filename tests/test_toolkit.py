import json

import pytest

from igniteai_agent_sdk._agent_context import Context
from igniteai_agent_sdk.llm.types import ChatCompletionMessage as Message
from igniteai_agent_sdk.llm.types import ChatCompletionMessageToolCall, Function
from igniteai_agent_sdk.tools.base_tool import Tool
from igniteai_agent_sdk.tools.toolkit import ToolK<PERSON>
from igniteai_agent_sdk.types import Result, ToolResponse


class TestToolKit:
    def test_initialization_empty(self):
        """Test initializing an empty ToolKit."""
        toolkit = ToolKit()

        assert hasattr(toolkit, "_tools")
        assert isinstance(toolkit._tools, dict)
        assert len(toolkit._tools) == 0

    def test_initialization_with_tools(self, sample_tool_function):
        """Test initializing ToolKit with tools."""
        toolkit = ToolKit([sample_tool_function])

        assert len(toolkit._tools) == 1
        assert "echo" in toolkit._tools
        assert isinstance(toolkit._tools["echo"], Tool)

    def test_add_tool(self, sample_tool_function):
        """Test adding a tool to ToolKit."""
        toolkit = ToolKit()
        toolkit._add_tool(sample_tool_function)

        assert len(toolkit._tools) == 1
        assert "echo" in toolkit._tools

    def test_add_tools(self, sample_tool_function):
        """Test adding multiple tools to ToolKit."""

        def another_function(value: int) -> int:
            """Return the input value.

            Args:
                value: The value to return

            """
            return value

        toolkit = ToolKit()
        toolkit._add_tools([sample_tool_function, another_function])

        assert len(toolkit._tools) == 2
        assert "echo" in toolkit._tools
        assert "another_function" in toolkit._tools

    def test_get_tools(self, sample_tool_function):
        """Test getting tools from ToolKit."""
        toolkit = ToolKit([sample_tool_function])
        tools = toolkit.get_tools()

        assert isinstance(tools, dict)
        assert "echo" in tools
        assert isinstance(tools["echo"], Tool)

    def test_get_json_schema(self, sample_tool_function):
        """Test generating JSON schema for tools."""
        toolkit = ToolKit([sample_tool_function])
        schema = toolkit.get_json_schema()

        assert isinstance(schema, list)
        assert len(schema) == 1
        assert schema[0]["type"] == "function"
        assert schema[0]["function"]["name"] == "echo"
        assert "description" in schema[0]["function"]
        assert "parameters" in schema[0]["function"]

    def test_get_json_schema_with_names(self, sample_tool_function):
        """Test generating JSON schema for specific tools by name."""

        def another_function(value: int) -> int:
            """Return the input value.

            Args:
                value: The value to return

            """
            return value

        toolkit = ToolKit([sample_tool_function, another_function])
        schema = toolkit.get_json_schema(["echo"])

        assert len(schema) == 1
        assert schema[0]["function"]["name"] == "echo"

    def test_get_string_schema(self, sample_tool_function):
        """Test generating string schema for tools."""
        toolkit = ToolKit([sample_tool_function])
        schema_str = toolkit.get_string_schema()

        assert isinstance(schema_str, str)
        schema_parsed = json.loads(schema_str)
        assert isinstance(schema_parsed, list)
        assert schema_parsed[0]["function"]["name"] == "echo"

    def test_handle_tool_result_string(self):
        """Test handling string tool results."""
        toolkit = ToolKit()
        result = toolkit.handle_tool_result("test result")

        assert isinstance(result, Result)
        assert result.value == "test result"

    def test_handle_tool_result_result_object(self):
        """Test handling Result tool results."""
        toolkit = ToolKit()
        input_result = Result(value="test result")
        result = toolkit.handle_tool_result(input_result)

        assert result is input_result
        assert result.value == "test result"

    @pytest.mark.asyncio
    async def test_execute_single_tool(self, sample_tool_function):
        """Test executing a single tool."""
        toolkit = ToolKit([sample_tool_function])

        # Create a mock tool call
        tool_call = {"function": {"name": "echo", "arguments": json.dumps({"message": "hello world"})}, "id": "test-id"}

        result, message = await toolkit._execute_single_tool(tool_call)

        assert isinstance(result, Result)
        assert result.value == "hello world"
        assert isinstance(message, Message)
        assert message.role == "tool"
        assert message.name == "echo"
        assert message.content == "hello world"
        assert message.tool_call_id == "test-id"

    @pytest.mark.asyncio
    async def test_execute_single_tool_with_context(self):
        """Test executing a tool that requires context."""

        def context_tool(message: str, context: Context) -> str:
            """A tool that needs context.

            Args:
                message: The message to process
                context: The agent context

            """
            return f"{message} with context"

        context = Context(context={"key": "value"})
        toolkit = ToolKit([context_tool])

        tool_call = {"function": {"name": "context_tool", "arguments": json.dumps({"message": "hello"})}, "id": "test-id"}

        result, message = await toolkit._execute_single_tool(tool_call, context=context)

        assert result.value == "hello with context"

    @pytest.mark.asyncio
    async def test_execute_single_tool_missing_tool(self):
        """Test handling missing tool errors."""
        toolkit = ToolKit()

        tool_call = {"function": {"name": "nonexistent_tool", "arguments": json.dumps({"message": "hello"})}, "id": "test-id"}

        with pytest.raises(ValueError, match="Tool 'nonexistent_tool' not registered"):
            await toolkit._execute_single_tool(tool_call)

    @pytest.mark.asyncio
    async def test_execute_tools(self, sample_tool_function):
        """Test executing multiple tools."""

        def add(a: int, b: int) -> int:
            """Add two numbers.

            Args:
                a: First number
                b: Second number

            """
            return a + b

        toolkit = ToolKit([sample_tool_function, add])

        # Create mock tool calls
        function1 = Function(name="echo", arguments='{"message": "test message"}')
        function2 = Function(name="add", arguments='{"a": 2, "b": 3}')

        tool_call1 = ChatCompletionMessageToolCall(id="id1", type="function", function=function1)

        tool_call2 = ChatCompletionMessageToolCall(id="id2", type="function", function=function2)

        response = await toolkit.execute_tools([tool_call1, tool_call2])

        assert isinstance(response, ToolResponse)
        assert response.agent_name != "test-agent"
        assert len(response.results) == 2
        assert len(response.messages) == 2

        # Check first result
        assert response.results[0].value == "test message"

        # Check second result
        assert response.results[1].value == "5"  # String conversion of int result

        # Check messages
        assert response.messages[0].name == "echo"
        assert response.messages[0].content == "test message"
        assert response.messages[1].name == "add"
        assert response.messages[1].content == "5"
